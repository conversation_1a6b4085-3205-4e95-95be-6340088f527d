// ==UserScript==
// @name         淘金幣兑换優化
// @namespace    http://tampermonkey.net/
// @version      2.2
// @description  Automatically click the specified reward button (1500, 3000, or 6000), reload if "已兑完", click confirm button, repeat until paused, with UI for control
// <AUTHOR> (optimized for user)
// @match        https://huodong.taobao.com/wow/z/tbhome/pc-growth/tao-coin
// @grant        GM_addStyle
// @grant        GM_getValue
// @grant        GM_setValue
// ==/UserScript==

(function() {
    'use strict';

    // 設置變數
    let isRunning = GM_getValue('isRunning', false);
    let targetPoints = GM_getValue('targetPoints', '1500'); // 預設1500
    let checkInterval = null;
    let isInExchangeMode = false; // 新增：是否進入兌換模式
    let targetButton = null; // 新增：保存目標按鈕引用

    // 創建UI
    function createUI() {
        // 檢查是否已存在UI，避免重複創建
        if (document.getElementById('auto-click-ui')) return;

        const ui = document.createElement('div');
        ui.id = 'auto-click-ui';
        ui.innerHTML = `
            <div style="position: fixed; top: 10px; right: 10px; background: #fff; border: 1px solid #ccc; padding: 10px; z-index: 9999; border-radius: 5px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                <h3 style="margin: 0 0 10px 0;">淘金幣兑换</h3>
                <label>兑换目標:</label>
                <select id="targetPoints" style="margin: 5px;">
                    <option value="1500" ${targetPoints === '1500' ? 'selected' : ''}>1500兑换</option>
                    <option value="3000" ${targetPoints === '3000' ? 'selected' : ''}>3000兑换</option>
                    <option value="6000" ${targetPoints === '6000' ? 'selected' : ''}>6000兑换</option>
                </select>
                <br>
                <button id="startBtn">開始</button>
                <button id="pauseBtn" disabled>暫停</button>
                <p style="margin: 10px 0 0 0;">狀態: <span id="status">已停止</span></p>
                <p style="margin: 5px 0 0 0; font-size: 12px;">模式: <span id="mode">搜尋中</span></p>
            </div>
        `;
        document.body.appendChild(ui);

        // 添加樣式
        GM_addStyle(`
            #auto-click-ui { font-family: Arial, sans-serif; }
            #auto-click-ui button { margin: 5px; padding: 5px 10px; cursor: pointer; }
            #auto-click-ui select { padding: 5px; width: 100px; }
            #status { color: #333; font-weight: bold; }
            #mode { color: #666; font-style: italic; }
            #startBtn { background: #4CAF50; color: white; border: none; border-radius: 3px; }
            #pauseBtn { background: #f44336; color: white; border: none; border-radius: 3px; }
            #startBtn:disabled { background: #cccccc; cursor: not-allowed; }
            #pauseBtn:disabled { background: #cccccc; cursor: not-allowed; }
        `);

        // 綁定事件
        document.getElementById('startBtn').addEventListener('click', startScript);
        document.getElementById('pauseBtn').addEventListener('click', pauseScript);
        document.getElementById('targetPoints').addEventListener('change', (e) => {
            targetPoints = e.target.value;
            GM_setValue('targetPoints', targetPoints);
        });

        // 初始化按鈕狀態
        updateButtonStates();
    }

    // 更新按鈕狀態
    function updateButtonStates() {
        const startBtn = document.getElementById('startBtn');
        const pauseBtn = document.getElementById('pauseBtn');
        if (startBtn && pauseBtn) {
            startBtn.disabled = isRunning;
            pauseBtn.disabled = !isRunning;
            updateStatus(isRunning ? '運行中...' : '已停止');
        }
    }

    // 更新狀態顯示
    function updateStatus(text) {
        const statusElement = document.getElementById('status');
        if (statusElement) {
            statusElement.textContent = text;
        }
    }

    // 更新模式顯示
    function updateMode(text) {
        const modeElement = document.getElementById('mode');
        if (modeElement) {
            modeElement.textContent = text;
        }
    }

    // 開始腳本
    function startScript() {
        if (isRunning) return;
        isRunning = true;
        isInExchangeMode = false;
        targetButton = null;
        GM_setValue('isRunning', isRunning);
        targetPoints = document.getElementById('targetPoints').value;
        GM_setValue('targetPoints', targetPoints);
        updateButtonStates();
        updateMode('搜尋中');
        checkButton();
    }

    // 暫停腳本
    function pauseScript() {
        isRunning = false;
        isInExchangeMode = false;
        targetButton = null;
        GM_setValue('isRunning', isRunning);
        clearInterval(checkInterval);
        updateButtonStates();
        updateMode('已暫停');
    }

    // 檢查並點擊兑换按鈕
    function checkButton() {
        if (!isRunning) return;

        // 如果已經進入兌換模式，直接執行兌換循環
        if (isInExchangeMode && targetButton) {
            executeExchangeLoop();
            return;
        }

        const goods = document.querySelectorAll('.goods-num');
        let foundButton = null;

        // 查找目標兑换按鈕
        goods.forEach((item) => {
            const text = item.textContent;
            if (text.includes(`${targetPoints}兑换`)) {
                foundButton = item;
            }
        });

        // 如果找到兑换按鈕，進入兌換模式
        if (foundButton) {
            console.log(`找到 ${targetPoints} 兑换按鈕，進入兌換模式`);
            updateStatus(`找到 ${targetPoints} 兑换按鈕`);
            updateMode('兌換模式');
            targetButton = foundButton;
            isInExchangeMode = true;
            executeExchangeLoop();
            return;
        }

        // 檢查是否已兑完
        let isSoldOut = false;
        goods.forEach((item) => {
            const text = item.textContent;
            if (text.includes(`${targetPoints}`) && text.includes('已兑完')) {
                isSoldOut = true;
            }
        });

        if (isSoldOut) {
            console.log(`${targetPoints} 已兑完，重新整理頁面...`);
            updateStatus(`${targetPoints} 已兑完，重新整理頁面...`);
            updateMode('重新整理中');
            setTimeout(() => location.reload(), 1000); // 1秒後重整
        } else {
            console.log(`未找到 ${targetPoints} 兑换按鈕，等待重試...`);
            updateStatus(`未找到 ${targetPoints} 兑换按鈕，等待重試...`);
            updateMode('搜尋中');
            checkInterval = setTimeout(checkButton, 1000); // 1秒後重試
        }
    }

    // 執行兌換循環（新增函數）
    function executeExchangeLoop() {
        if (!isRunning || !isInExchangeMode || !targetButton) return;

        console.log(`執行兌換循環 - ${targetPoints}`);
        updateStatus(`持續兌換 ${targetPoints}...`);
        
        // 點擊兌換按鈕
        targetButton.click();
        
        // 等待並點擊確認按鈕
        setTimeout(() => {
            if (!isRunning) return;
            clickConfirmButton();
        }, 500);

        // 設置下次循環
        checkInterval = setTimeout(() => {
            if (isRunning && isInExchangeMode) {
                executeExchangeLoop();
            }
        }, 300); // 0.3秒後再次執行
    }

    // 點擊確認按鈕（優化版）
    function clickConfirmButton() {
        if (!isRunning) return;

        const confirmButton = document.querySelector('.btn.confirm');
        if (confirmButton) {
            console.log('點擊確認按鈕');
            confirmButton.click();
        } else {
            console.log('未找到確認按鈕，繼續嘗試...');
            // 如果沒找到確認按鈕，短暫等待後再試
            setTimeout(clickConfirmButton, 300);
        }
    }

    // 初始化
    createUI();
    // 檢查保存的狀態，自動恢復運行
    if (isRunning) {
        updateButtonStates();
        checkButton();
    }
})();
