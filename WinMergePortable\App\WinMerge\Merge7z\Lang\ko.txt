﻿;!@Lang2@!UTF-8!
;       : <PERSON><PERSON><PERSON><PERSON><PERSON> (임재형)
;       : bzImage
;  4.52 : <PERSON><PERSON><PERSON> (<PERSON><PERSON>)
;  9.07 : <PERSON><PERSON><PERSON><PERSON> (한동윤)
; 15.12 : Winterscenery (Ji-yong BAE)
; 16.04 : Add translation and Modify by Winterscenery (Ji-yong BAE)
;
;
;
;
;
0
7-Zip
Korean
한국어
401
확인
취소



예(&Y)
아니오(&N)
닫기(&C)
도움말

계속(&C)
440
모두 예(&A)
모두 아니오(&L)
중지
다시 시작
낮은 순위로(&B)
우선 순위로(&F)
일시정지(&P)
일시정지 됨
정말로 취소하시겠습니까?
500
파일(&F)
편집(&E)
보기(&V)
즐겨찾기(&A)
도구(&T)
도움말(&H)
540
열기(&O)
내부 열기(&I)
외부 열기(&U)
파일 뷰어(&V)
편집(&E)
새 이름(&M)
복사(&C)...
이동(&M)...
삭제(&D)
파일 나누기(&S)...
파일 합치기(&B)...
속성(&R)
설명(&N)...
체크섬 계산
파일 비교
폴더 만들기
파일 만들기
끝내기(&X)
연결
대체 데이터 스트림(&A)
600
모두 선택(&A)
모두 선택 취소
선택 항목 반전(&I)
선택...
선택 취소...
파일 형식으로 선택
파일 형식으로 선택 취소
700
큰 아이콘(&G)
작은 아이콘(&M)
목록(&L)
자세히(&S)
730
정렬 안함
펼쳐 보기
2 패널(&2)
도구 모음(&T)
최상위 폴더 열기
한단계 위로
폴더 히스토리...
새로 고침(&R)
자동 새로 고침
750
압축 도구 모음
표준 도구 모음
큰 버튼
버튼 텍스트 보기
800
즐겨찾기에 추가(&A)
북마크
900
옵션(&P)...
벤치마크(&B)
960
도움말 항목(&C)...
7-Zip 정보(&A)...
1003
경로
이름
확장자
폴더
크기
압축된 크기
속성
만든 날짜
액세스한 날짜
수정한 날짜
솔리드
설명
암호화
나누기 이전
나누기 이후
사전
CRC
형식
안티
압축 방식
생성한 OS
파일 시스템
사용자
그룹
블럭
설명
위치
경로 접두
폴더
파일
버전
볼륨
다중볼륨
오프셋
연결
블록
볼륨

64-bit
Big-endian
CPU
물리적 크기
해더 크기
체크섬
특성
가상 주소
ID
짧은 이름
생성 응용프로그램
섹터 크기
모드
심볼릭 연결
오류
전체 크기
여유 공간
클러스터 크기
라벨
로컬 이름
제공자
NT 보안
대체 데이터 스트림
보조
삭제
Is Tree


오류 유형
오류
오류
경고
경고
스트림
대체 데이터 스트림
대체 데이터 스트림 크기
가상 크기
압축 해제 후 크기
용량 실제 크기
볼륨 인덱스
보조형식
주석
코드 페이지



테일 크기
임베디드 스텁 크기
연결
하드 연결
iNode

읽기 전용
2100
옵션
언어
언어:
편집기
파일 편집기(&E):
파일 비교(&D):
2200
시스템
7-Zip 으로 연결:
모든 사용자
2301
7-Zip 탐색기 메뉴 사용
탐색기 메뉴 계단식 보기
탐색기 메뉴 항목:
탐색기 메뉴 아이콘 보기
2320
<폴더>
<압축파일>
압축파일 열기
압축 풀기...
압축파일에 추가...
압축파일 테스트
여기에 압축 풀기
{0}에 풀기
{0}에 추가
압축해서 이메일 보내기
{0}로 압축해서 이메일 보내기
2400
폴더
작업 폴더(&W)
시스템 임시 폴더(&S)
현재 폴더(&C)
지정 폴더(&S):
이동식 드라이브에서만 사용
압축에 관계된 파일이 임시적으로 사용할 위치 지정.
2500
설정
상위 폴더 ".." 항목 보기
실제 파일 아이콘 보기
시스템 메뉴 보기
행 전체 선택(&F)
눈금선 보기(&G)
한 번 클릭으로 항목 열기
교차 선택 모드
큰 메모리 페이지 사용
2900
7-Zip 정보
7-Zip 은 무료 소프트웨어입니다.
3000
시스템이 필요한 양의 메모리를 할당할 수 없음
오류 없음
{0} 항목이 선택됨
'{0}' 폴더를 생성할 수 없음
업데이트 작업이 이 압축파일에서는 지원되지 않습니다.
파일 '{0}'을(를) 압축파일로 열 수 없음
암호화된 압축파일 '{0}'을(를) 열 수 없습니다. 잘못 입력된 암호?
지원되지 않는 압축파일 유형
{0} 파일은 이미 존재함
파일 '{0}'이 수정되었습니다.\n압축파일에 업데이트 하시겠습니까?
'{0}' 파일을 업데이트 할 수 없습니다.
편집기를 시작할 수 없습니다.
해당 파일이 바이러스 같습니다 (파일 이름에 길다란 공백이 들어있음).
긴 경로로된 폴더에서 해당 작업을 호출할 수 없습니다.
반드시 한 개의 파일을 선택해야함
반드시 한 개 이상의 파일을 선택해야 함
항목이 너무 많음
{0} 압축파일로 파일을 열 수 없습니다
{0} 압축파일로 파일이 열리고 있습니다
압축파일 오프셋을 사용하여 열려 있습니다
3300
압축 푸는 중
압축하는 중
검사 중
여는 중...
검색 중...
삭제 중
3320
추가 중
업데이트 중
분석 중
덮어 쓰는 중
다시 압축 중
건너뛰는 중
삭제 중
헤더 작성 중
3400
압축 풀기
압축 풀기(&X):
압축 풀린 파일의 위치를 지정합니다.
3410
경로 모드
전체 경로명
경로명 없음
절대 경로명
상대 경로명
3420
덮어쓰기 모드
덮어쓰기 전에 확인
물어보지 않고 덮어쓰기
존재하는 파일 건너뛰기
자동으로 이름 바꾸기
존재하는 파일 이름 바꾸기
3430
최상위 폴더의 중복을 방지
파일 보안 속성을 복원
3500
파일 덮어쓰기 확인
대상 폴더에 이미 파일이 존재합니다.
존재하는 파일을
이것으로 덮어쓰기 하시겠습니까?
{0} 바이트
자동으로 이름 바꾸기(&U)
3700
'{0}'은 지원하지 않는 압축 방식입니다.
'{0}'에 데이터 오류가 있습니다. 파일이 손상되었습니다.
'{0}'의 CRC 검사를 실패했습니다. 파일이 손상되었습니다.
암호화 파일 '{0}'에 데이터 오류가 있습니다. 암호가 틀리나요?
암호화 파일 '{0}'의 CRC 검사를 실패했습니다. 암호가 틀리나요?
3710
잘못 입력된 암호?
3721
지원하지 않는 압축 방법
데이터 오류
CRC가 다릅니다
사용할 수없는 데이터
데이터의 예기치 않은 종료
페이로드 데이터 종료 후에 일부 데이터가 있습니다.
압축되지 않았습니다.
헤더 오류
암호가 잘못되었습니다.
3763
사용할 수 없는 시작의  압축파일
확인되지 않는 시작의 압축파일



지원하지 않는 기능
3800
암호 입력
암호 입력:
암호 다시 입력:
암호 보기(&S)
암호가 일치하지 않음
암호로는 영문자, 숫자 그리고 특수 문자 (!, #, $, ...)만 사용
암호가 너무 깁니다.
암호
3900
경과 시간:
남은 시간:
전체 크기:
속도:
처리됨:
압축 효율:
오류:
압축파일:
4000
압축파일에 추가
압축파일(&A):
업데이트 모드(&U):
압축파일 형식(&F):
압축 레벨(&L):
압축 방식(&M):
사전 크기(&D):
단어(word) 크기(&W):
솔리드 블록 크기:
CPU 스레드 수:
매개변수(&P):
옵션
자동(SFX) 압축파일 생성(&X)
공유하고있는 파일 압축
암호화
암호화 방식:
파일 이름 암호화(&N)
압축시 사용 메모리:
압축 풀기시 사용 메모리:
압축 후 원본 파일을 삭제
4040
심볼릭 연결을 저장
하드 연결을 저장
대체 데이터 스트림을 저장
파일 보안 속성을 저장
4050
저장
가장 빠름
빠름
보통
최고
가장 느림
4060
추가 파일 덮어쓰기
업데이트 후 파일 추가
기존 파일을 새로 고침
파일을 동기화
4070
찾아보기
모든 파일
솔리드 사용 않함
솔리드
6000
복사
이동
폴더로 복사:
폴더로 이동:
복사 중...
이동 중...
이름 바꾸는 중...
대상 폴더를 선택하세요.
지원되지 않는 작업입니다.
파일 또는 폴더 이름 바꾸기 실패
파일 복사 확인
파일을 압축파일로 복사 하시겠습니까?
6100
파일 삭제 확인
폴더 삭제 확인
여러 파일 지우기 확인
'{0}'을(를) 삭제하시겠습니까?
폴더 '{0}'와 그 모든 내용을 삭제하시겠습니까?
이 {0} 항목들을 삭제하시겠습니까?
삭제 중...
파일 또는 폴더 삭제 실패
시스템이 긴 경로의 파일을 휴지통으로 이동할 수 없음
6300
폴더 만들기
파일 만들기
폴더 이름:
파일 이름:
새 폴더
새 파일
폴더 만들기 오류
파일 만들기 오류
6400
설명
설명(&C):
선택
선택 취소
마스크:
6600
속성
폴더 히스토리
진단 메시지
메시지
7100
컴퓨터
네트워크
문서
시스템
7200
추가
압축 풀기
테스트
복사
이동
삭제
속성
7300
파일 분할하기
분할하기(&S):
볼륨 나누기, 바이트(&V):
분할하는 중...
분할 확인
정말 {0} 볼륨들로 분할 하시겠습니까?
볼륨 크기가 원본 파일보다 작아야만 합니다.
볼륨 크기가 부적절합니다.
지정된 볼륨 크기: {0} 바이트.\n이 볼륨 크기로 분할 하시겠습니까?
7400
파일 합치기
합치기(&B):
파일 합치는 중...
첫번째 파일만 선택하시오
분할한 파일의 한 부분으로 인식할 수 없음
분할 파일의 한 부분 이상을 찾을 수 없음
7500
체크섬 계산중...
체크섬 정보
데이터 CRC 체크섬:
데이터와 이름 CRC 체크섬:
7600
벤치마크
메모리 사용량:
압축 중
압축 푸는 중
평가
전체 평가
현재
결과
CPU 사용량
평가 / 사용량
통과:
7700
연결
연결 만들기
원본:
연결:
7710
연결 유형
하드 연결
파일의 심볼릭 연결
디렉토리의 심볼릭 연결
디렉토리 접합
