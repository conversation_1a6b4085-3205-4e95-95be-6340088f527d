import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import os
import sys
import re
from PIL import Image, ImageTk
import io

# 檢查並提示安裝必要的套件
def check_dependencies():
    """檢查必要的依賴套件"""
    missing_packages = []
    
    try:
        import pytesseract
    except ImportError:
        missing_packages.append("pytesseract")
    
    try:
        import cv2
    except ImportError:
        missing_packages.append("opencv-python")
    
    if missing_packages:
        error_msg = f"""
缺少必要的套件，請先安裝：

{chr(10).join([f'pip install {pkg}' for pkg in missing_packages])}

另外，您還需要安裝 Tesseract OCR 引擎：
1. 下載並安裝 Tesseract: https://github.com/UB-Mannheim/tesseract/wiki
2. 將 Tesseract 安裝路徑添加到系統 PATH 環境變數中
   或在程式中指定路徑（通常是 C:\\Program Files\\Tesseract-OCR\\tesseract.exe）

安裝完成後請重新執行程式。
        """
        messagebox.showerror("缺少依賴套件", error_msg)
        return False
    
    return True

class OCRImageCalculator:
    def __init__(self, root):
        self.root = root
        self.root.title("OCR 圖片計算器")
        self.root.geometry("1000x700")
        
        # 檢查依賴套件
        if not check_dependencies():
            self.root.destroy()
            return
            
        # 導入OCR相關模組
        try:
            import pytesseract
            import cv2
            import numpy as np
            self.pytesseract = pytesseract
            self.cv2 = cv2
            self.np = np
        except ImportError as e:
            messagebox.showerror("導入錯誤", f"無法導入必要模組: {str(e)}")
            self.root.destroy()
            return
        
        self.current_image = None
        self.processed_image = None
        self.ocr_text = ""
        self.extracted_numbers = []
        
        # 設定 Tesseract 路徑（如果需要）
        self.setup_tesseract()
        
        self.setup_ui()
        
    def setup_tesseract(self):
        """設定 Tesseract OCR 引擎路徑"""
        # 常見的 Tesseract 安裝路徑
        possible_paths = [
            r"C:\Program Files\Tesseract-OCR\tesseract.exe",
            r"C:\Program Files (x86)\Tesseract-OCR\tesseract.exe",
            r"C:\Users\<USER>\AppData\Local\Tesseract-OCR\tesseract.exe".format(os.getenv('USERNAME', '')),
            "tesseract"  # 如果已添加到 PATH
        ]
        
        for path in possible_paths:
            if path == "tesseract" or os.path.exists(path):
                try:
                    self.pytesseract.pytesseract.tesseract_cmd = path
                    # 測試是否可以正常使用
                    self.pytesseract.get_tesseract_version()
                    return
                except:
                    continue
        
        # 如果找不到 Tesseract，提示用戶
        messagebox.showwarning(
            "Tesseract 未找到", 
            "未找到 Tesseract OCR 引擎。\n"
            "請確保已安裝 Tesseract 並添加到系統 PATH，\n"
            "或手動設定路徑。"
        )
    
    def setup_ui(self):
        """設定使用者介面"""
        # 工具列
        toolbar = ttk.Frame(self.root)
        toolbar.pack(side=tk.TOP, fill=tk.X, padx=5, pady=5)
        
        ttk.Button(toolbar, text="開啟圖片", command=self.open_image).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar, text="執行OCR", command=self.perform_ocr).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar, text="提取數值", command=self.extract_numbers).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar, text="自定義計算", command=self.custom_calculate).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar, text="計算結果", command=self.calculate_results).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar, text="清除結果", command=self.clear_results).pack(side=tk.LEFT, padx=2)

        # 第二行工具列 - 自定義公式
        toolbar2 = ttk.Frame(self.root)
        toolbar2.pack(side=tk.TOP, fill=tk.X, padx=5, pady=2)

        ttk.Label(toolbar2, text="自定義公式:").pack(side=tk.LEFT, padx=2)
        self.formula_var = tk.StringVar(value="(([4]-[3])/3)+[3]")
        self.formula_entry = ttk.Entry(toolbar2, textvariable=self.formula_var, width=30)
        self.formula_entry.pack(side=tk.LEFT, padx=2)
        ttk.Label(toolbar2, text="(使用[1]-[9]代表數值位置，其他數字為常數)").pack(side=tk.LEFT, padx=2)

        # 主要內容區域
        main_frame = ttk.PanedWindow(self.root, orient=tk.HORIZONTAL)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 左側：圖片顯示區域
        left_frame = ttk.Frame(main_frame)
        main_frame.add(left_frame, weight=1)
        
        ttk.Label(left_frame, text="圖片預覽").pack(anchor=tk.W)
        
        # 圖片顯示畫布
        self.image_frame = ttk.Frame(left_frame, relief='sunken', borderwidth=2)
        self.image_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        self.canvas = tk.Canvas(self.image_frame, bg='white')
        v_scrollbar = ttk.Scrollbar(self.image_frame, orient='vertical', command=self.canvas.yview)
        h_scrollbar = ttk.Scrollbar(self.image_frame, orient='horizontal', command=self.canvas.xview)
        
        self.canvas.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        self.canvas.grid(row=0, column=0, sticky='nsew')
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')
        
        self.image_frame.grid_rowconfigure(0, weight=1)
        self.image_frame.grid_columnconfigure(0, weight=1)
        
        # 右側：文字和計算結果區域
        right_frame = ttk.Frame(main_frame)
        main_frame.add(right_frame, weight=1)
        
        # OCR 結果顯示
        ttk.Label(right_frame, text="OCR 識別結果").pack(anchor=tk.W)
        self.ocr_text_widget = scrolledtext.ScrolledText(right_frame, height=10, wrap=tk.WORD)
        self.ocr_text_widget.pack(fill=tk.BOTH, expand=True, pady=5)
        
        # 數值提取結果
        ttk.Label(right_frame, text="提取的數值").pack(anchor=tk.W)
        self.numbers_text_widget = scrolledtext.ScrolledText(right_frame, height=5, wrap=tk.WORD)
        self.numbers_text_widget.pack(fill=tk.BOTH, expand=True, pady=5)
        
        # 計算結果
        ttk.Label(right_frame, text="計算結果").pack(anchor=tk.W)
        self.result_text_widget = scrolledtext.ScrolledText(right_frame, height=8, wrap=tk.WORD)
        self.result_text_widget.pack(fill=tk.BOTH, expand=True, pady=5)
        
        # 狀態列
        self.status_bar = ttk.Label(self.root, text="請選擇圖片檔案開始OCR識別")
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)

    def open_image(self):
        """開啟圖片檔案"""
        file_path = filedialog.askopenfilename(
            title="選擇圖片檔案",
            filetypes=[
                ("圖片檔案", "*.png *.jpg *.jpeg *.gif *.bmp *.tiff *.tif"),
                ("PNG files", "*.png"),
                ("JPEG files", "*.jpg *.jpeg"),
                ("所有檔案", "*.*")
            ]
        )

        if file_path:
            try:
                # 載入圖片
                self.current_image = Image.open(file_path)

                # 顯示圖片
                self.display_image(self.current_image)

                # 清除之前的結果
                self.clear_results()

                self.status_bar.config(text=f"已載入圖片: {os.path.basename(file_path)}")

            except Exception as e:
                messagebox.showerror("錯誤", f"無法開啟圖片檔案: {str(e)}")

    def display_image(self, image):
        """在畫布上顯示圖片"""
        try:
            # 計算適合的顯示尺寸
            canvas_width = self.canvas.winfo_width()
            canvas_height = self.canvas.winfo_height()

            if canvas_width <= 1 or canvas_height <= 1:
                # 如果畫布尺寸還沒初始化，使用預設值
                canvas_width = 400
                canvas_height = 300

            # 計算縮放比例以適應畫布
            img_width, img_height = image.size
            scale_x = canvas_width / img_width
            scale_y = canvas_height / img_height
            scale = min(scale_x, scale_y, 1.0)  # 不放大，只縮小

            # 調整圖片大小
            new_width = int(img_width * scale)
            new_height = int(img_height * scale)

            display_image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)

            # 轉換為 PhotoImage
            self.photo = ImageTk.PhotoImage(display_image)

            # 清除畫布並顯示圖片
            self.canvas.delete("all")
            self.canvas.create_image(
                new_width//2, new_height//2,
                anchor='center',
                image=self.photo
            )

            # 設定滾動區域
            self.canvas.configure(scrollregion=self.canvas.bbox("all"))

        except Exception as e:
            messagebox.showerror("錯誤", f"無法顯示圖片: {str(e)}")

    def perform_ocr(self):
        """執行OCR識別"""
        if not self.current_image:
            messagebox.showwarning("警告", "請先開啟圖片檔案")
            return

        try:
            self.status_bar.config(text="正在執行OCR識別...")
            self.root.update()

            # 將PIL圖片轉換為OpenCV格式進行預處理
            img_array = self.np.array(self.current_image)

            # 如果是RGBA，轉換為RGB
            if len(img_array.shape) == 3 and img_array.shape[2] == 4:
                img_array = self.cv2.cvtColor(img_array, self.cv2.COLOR_RGBA2RGB)

            # 轉換為灰度圖
            if len(img_array.shape) == 3:
                gray = self.cv2.cvtColor(img_array, self.cv2.COLOR_RGB2GRAY)
            else:
                gray = img_array

            # 圖片預處理以提高OCR準確性
            # 1. 高斯模糊去噪
            blurred = self.cv2.GaussianBlur(gray, (5, 5), 0)

            # 2. 自適應閾值二值化
            binary = self.cv2.adaptiveThreshold(
                blurred, 255, self.cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                self.cv2.THRESH_BINARY, 11, 2
            )

            # 3. 形態學操作去除噪點
            kernel = self.np.ones((2, 2), self.np.uint8)
            processed = self.cv2.morphologyEx(binary, self.cv2.MORPH_CLOSE, kernel)

            # 將處理後的圖片轉換回PIL格式
            self.processed_image = Image.fromarray(processed)

            # 執行OCR識別（支援中文）
            custom_config = r'--oem 3 --psm 6 -l chi_tra+eng'
            try:
                # 先嘗試中英文混合識別
                self.ocr_text = self.pytesseract.image_to_string(
                    self.processed_image,
                    config=custom_config
                )
            except:
                # 如果中文識別失敗，嘗試只用英文
                try:
                    self.ocr_text = self.pytesseract.image_to_string(
                        self.processed_image,
                        config=r'--oem 3 --psm 6 -l eng'
                    )
                except:
                    # 最後嘗試預設設定
                    self.ocr_text = self.pytesseract.image_to_string(self.processed_image)

            # 顯示OCR結果
            self.ocr_text_widget.delete(1.0, tk.END)
            self.ocr_text_widget.insert(1.0, self.ocr_text)

            self.status_bar.config(text="OCR識別完成")

        except Exception as e:
            messagebox.showerror("錯誤", f"OCR識別失敗: {str(e)}")
            self.status_bar.config(text="OCR識別失敗")

    def extract_numbers(self):
        """從OCR文字中提取數值"""
        if not self.ocr_text:
            messagebox.showwarning("警告", "請先執行OCR識別")
            return

        try:
            # 使用正則表達式提取數字（包括小數、負數、百分比等）
            patterns = [
                r'-?\d+\.?\d*%?',  # 基本數字（包括百分比）
                r'-?\d{1,3}(?:,\d{3})*\.?\d*',  # 帶千分位逗號的數字
                r'-?\d+/\d+',  # 分數
                r'-?\d+\.\d+[eE][+-]?\d+',  # 科學記號
            ]

            all_numbers = []
            for pattern in patterns:
                matches = re.findall(pattern, self.ocr_text)
                all_numbers.extend(matches)

            # 轉換為數值並去重
            self.extracted_numbers = []
            seen_numbers = set()

            for match in all_numbers:
                try:
                    # 處理百分比
                    if match.endswith('%'):
                        num = float(match[:-1]) / 100
                        if num not in seen_numbers:
                            self.extracted_numbers.append(num)
                            seen_numbers.add(num)
                    # 處理帶逗號的數字
                    elif ',' in match and '/' not in match:
                        num = float(match.replace(',', ''))
                        if num not in seen_numbers:
                            self.extracted_numbers.append(num)
                            seen_numbers.add(num)
                    # 處理分數
                    elif '/' in match:
                        parts = match.split('/')
                        if len(parts) == 2:
                            num = float(parts[0]) / float(parts[1])
                            if num not in seen_numbers:
                                self.extracted_numbers.append(num)
                                seen_numbers.add(num)
                    # 處理一般數字
                    else:
                        num = float(match)
                        if num not in seen_numbers:
                            self.extracted_numbers.append(num)
                            seen_numbers.add(num)
                except ValueError:
                    continue

            # 顯示提取的數值
            self.numbers_text_widget.delete(1.0, tk.END)
            if self.extracted_numbers:
                numbers_text = "提取到的數值:\n"
                for i, num in enumerate(self.extracted_numbers, 1):
                    numbers_text += f"{i}. {num}\n"
                self.numbers_text_widget.insert(1.0, numbers_text)
                self.status_bar.config(text=f"成功提取 {len(self.extracted_numbers)} 個數值")
            else:
                self.numbers_text_widget.insert(1.0, "未找到任何數值")
                self.status_bar.config(text="未找到任何數值")

        except Exception as e:
            messagebox.showerror("錯誤", f"數值提取失敗: {str(e)}")

    def custom_calculate(self):
        """執行自定義公式計算"""
        if not self.extracted_numbers:
            messagebox.showwarning("警告", "請先提取數值")
            return

        try:
            formula = self.formula_var.get().strip()
            if not formula:
                messagebox.showwarning("警告", "請輸入計算公式")
                return

            numbers = self.extracted_numbers

            # 顯示當前數值列表
            result_text = "=== 自定義公式計算 ===\n\n"
            result_text += f"\n原始公式: {formula}\n"

            # 替換公式中的位置索引為實際數值
            # 使用 [數字] 格式來標識位置索引，其他數字保持為常數
            import re
            calculated_formula = formula

            # 替換 [數字] 格式的位置索引
            for i in range(min(9, len(numbers)), 0, -1):
                pattern = r'\[' + str(i) + r'\]'
                calculated_formula = re.sub(pattern, f"({numbers[i-1]})", calculated_formula)

            result_text += f"替換後公式: {calculated_formula}\n"

            # 安全地計算公式
            try:
                # 只允許基本的數學運算
                allowed_chars = set('0123456789+-*/().e ')
                if not all(c in allowed_chars for c in calculated_formula):
                    raise ValueError("公式包含不允許的字符")

                # 計算結果
                result = eval(calculated_formula)
                result_text += f"\n計算結果: {result}\n"
                result_text += f"結果 (保留6位小數): {result:.6f}\n"

                # 如果結果是整數，也顯示整數形式
                if result == int(result):
                    result_text += f"結果 (整數): {int(result)}\n"

            except Exception as calc_error:
                result_text += f"\n計算錯誤: {str(calc_error)}\n"
                result_text += "請檢查公式是否正確\n"

            # 顯示結果
            self.result_text_widget.delete(1.0, tk.END)
            self.result_text_widget.insert(1.0, result_text)

            self.status_bar.config(text="自定義計算完成")

        except Exception as e:
            messagebox.showerror("錯誤", f"自定義計算失敗: {str(e)}")

    def calculate_results(self):
        """計算數值結果"""
        if not self.extracted_numbers:
            messagebox.showwarning("警告", "請先提取數值")
            return

        try:
            numbers = self.extracted_numbers

            # 執行各種計算
            results = []
            results.append("=== 計算結果 ===\n")
            results.append(f"數值個數: {len(numbers)}\n")
            results.append(f"所有數值: {numbers}\n\n")

            # 基本統計
            results.append("--- 基本統計 ---\n")
            results.append(f"總和: {sum(numbers):.6f}\n")
            results.append(f"平均值: {sum(numbers)/len(numbers):.6f}\n")
            results.append(f"最大值: {max(numbers):.6f}\n")
            results.append(f"最小值: {min(numbers):.6f}\n")
            results.append(f"範圍: {max(numbers) - min(numbers):.6f}\n\n")

            # 排序後的數值
            sorted_numbers = sorted(numbers)
            results.append("--- 排序結果 ---\n")
            results.append(f"由小到大: {sorted_numbers}\n")
            results.append(f"由大到小: {sorted(numbers, reverse=True)}\n\n")

            # 中位數
            n = len(sorted_numbers)
            if n % 2 == 0:
                median = (sorted_numbers[n//2-1] + sorted_numbers[n//2]) / 2
            else:
                median = sorted_numbers[n//2]
            results.append(f"中位數: {median:.6f}\n\n")

            # 如果有多個數值，計算更多統計資訊
            if len(numbers) > 1:
                # 變異數和標準差
                mean = sum(numbers) / len(numbers)
                variance = sum((x - mean) ** 2 for x in numbers) / len(numbers)
                std_dev = variance ** 0.5

                results.append("--- 進階統計 ---\n")
                results.append(f"變異數: {variance:.6f}\n")
                results.append(f"標準差: {std_dev:.6f}\n")

                # 四分位數
                q1_idx = n // 4
                q3_idx = 3 * n // 4
                results.append(f"第一四分位數 (Q1): {sorted_numbers[q1_idx]:.6f}\n")
                results.append(f"第三四分位數 (Q3): {sorted_numbers[q3_idx]:.6f}\n")
                results.append(f"四分位距 (IQR): {sorted_numbers[q3_idx] - sorted_numbers[q1_idx]:.6f}\n\n")

            # 特殊計算
            results.append("--- 特殊計算 ---\n")
            if all(x > 0 for x in numbers):
                # 幾何平均數（只有正數才能計算）
                geometric_mean = 1
                for x in numbers:
                    geometric_mean *= x
                geometric_mean = geometric_mean ** (1/len(numbers))
                results.append(f"幾何平均數: {geometric_mean:.6f}\n")

                # 調和平均數
                harmonic_mean = len(numbers) / sum(1/x for x in numbers)
                results.append(f"調和平均數: {harmonic_mean:.6f}\n")

            # 乘積
            product = 1
            for x in numbers:
                product *= x
            results.append(f"所有數值的乘積: {product:.6f}\n")

            # 顯示結果
            self.result_text_widget.delete(1.0, tk.END)
            self.result_text_widget.insert(1.0, ''.join(results))

            self.status_bar.config(text="計算完成")

        except Exception as e:
            messagebox.showerror("錯誤", f"計算失敗: {str(e)}")

    def clear_results(self):
        """清除所有結果"""
        self.ocr_text_widget.delete(1.0, tk.END)
        self.numbers_text_widget.delete(1.0, tk.END)
        self.result_text_widget.delete(1.0, tk.END)
        self.ocr_text = ""
        self.extracted_numbers = []
        self.status_bar.config(text="已清除所有結果")

def main():
    root = tk.Tk()
    app = OCRImageCalculator(root)
    root.mainloop()

if __name__ == "__main__":
    main()
