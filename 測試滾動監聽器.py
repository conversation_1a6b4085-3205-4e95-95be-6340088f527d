#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
滾動監聽器測試說明

這個滾動監聽器會幫助您追蹤文字框內文字滾動的原因。

使用方法：
1. 運行 20250624閱讀器.py
2. 開啟一個PDF檔案
3. 新增一個文字框
4. 在文字框中輸入一些文字
5. 設置字體大小為20（您提到這個大小容易滾動）
6. 嘗試移動文字框或進行其他操作
7. 點擊工具列上的「滾動事件」按鈕查看滾動記錄

監聽器會記錄：
- 滾動發生的時間
- 滾動前後的位置
- 當前的字體大小
- 觸發滾動的程式碼調用堆疊

這樣您就能看到：
- 什麼時候發生了滾動
- 是什麼操作觸發的滾動
- 字體大小是否與滾動有關

特別注意：
- 監聽器每100毫秒檢查一次滾動位置
- 會在控制台即時打印滾動事件
- 可以通過「滾動事件」按鈕查看詳細記錄
- 可以清除記錄重新開始監聽

常見滾動觸發原因：
1. 字體大小變更時的重新排版
2. 文字內容修改時的自動調整
3. 文字框大小調整時的重新計算
4. 紅色字符更新時的位置調整
5. 移動文字框時的意外滾動

如果發現字體大小20特別容易滾動，監聽器會幫您找出具體原因。
