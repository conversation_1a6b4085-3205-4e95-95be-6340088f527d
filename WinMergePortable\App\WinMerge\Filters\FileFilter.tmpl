## This is a directory/file filter template for WinMerge
name: Name of filter
desc: Longer description

## Select if filter is inclusive or exclusive
## Inclusive (loose) filter lets through all items not matching rules
## Exclusive filter lets through only items that match to rule
## include or exclude
def: include

## Filters for filenames begin with f:
## Filters for directories begin with d:
## (Inline comments begin with " ##" and extend to the end of the line)

f: \.ext$ ## Filter for filename

d: \\subdir$ ## Filter for directory
