<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>計算字數</title>
<style>
    .container {
        max-width: 400px;
        margin: 0 auto;
        padding: 20px;
        border: 1px solid #ccc;
        border-radius: 5px;
    }
    input[type="number"] {
        width: 100%;
        padding: 10px;
        margin-bottom: 10px;
        box-sizing: border-box;
    }
    button {
        padding: 10px 20px;
        background-color: #007bff;
        color: #fff;
        border: none;
        border-radius: 5px;
        cursor: pointer;
    }
    #result {
        margin-top: 10px;
        font-size: 18px;
    }
</style>
</head>
<body>
<div class="container">
    <input type="number" id="inputA" placeholder="字元數(不含空白)">
    <input type="number" id="inputB" placeholder="字數">
    <button onclick="calculate()">計算字數</button>
    <div id="result"></div>
</div>

<script>
    function calculate() {
        var inputA = parseFloat(document.getElementById('inputA').value);
        var inputB = parseFloat(document.getElementById('inputB').value);
        var result = (inputA - inputB) / 3 + inputB;
        document.getElementById('result').innerText = '字數：' + result;
    }
</script>
</body>
</html>
