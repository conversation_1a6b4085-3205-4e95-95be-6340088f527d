import tkinter as tk
from tkinter import messagebox
import win32com.client

def insert_bold_text_to_word(text):
    try:
        word_app = win32com.client.GetActiveObject("Word.Application")
        if word_app.Documents.Count == 0:
            return False, "沒有開啟的Word文件"
        selection = word_app.Selection
        selection.Font.Bold = True
        selection.TypeText(text)
        selection.Font.Bold = False
        return True, "成功插入文字"
    except Exception as e:
        error_msg = str(e)
        if "GetActiveObject" in error_msg:
            return False, "請先開啟Word應用程式"
        else:
            return False, f"插入失敗: {error_msg}"

class InsertRangeApp:
    def __init__(self, master):
        self.master = master
        self.master.title("範圍器（Word COM）")
        self.master.attributes("-topmost", True)
        self.master.geometry("300x130+1000+50")  # 增加高度以容納更大的按鈕

        top_frame = tk.Frame(master)
        top_frame.pack(pady=(8, 4))

        tk.Label(top_frame, text="起始：").pack(side=tk.LEFT, padx=(5, 2))
        self.start_entry = tk.Entry(top_frame, width=4)
        self.start_entry.pack(side=tk.LEFT, padx=(0, 10))
        self.start_entry.insert(0, "45")

        tk.Label(top_frame, text="結束：").pack(side=tk.LEFT, padx=(0, 2))
        self.end_entry = tk.Entry(top_frame, width=4)
        self.end_entry.pack(side=tk.LEFT, padx=(0, 10))
        self.end_entry.insert(0, "56")

        self.set_button = tk.Button(top_frame, text="設定範圍", command=self.set_range)
        self.set_button.pack(side=tk.LEFT)

        self.insert_button = tk.Button(
            master,
            text="",
            command=self.insert_next,
            font=("Arial", 16),
            width=20,
            height=4,  # 調高按鈕（4 行高度）
            state=tk.DISABLED
        )
        self.insert_button.pack(pady=(0, 5))

        # 綁定滑鼠右鍵事件
        self.master.bind('<Button-3>', lambda event: self.skip_to_next())

        self.current = None
        self.start = None
        self.end = None

    def skip_to_next(self):
        if self.current is None or self.current > self.end:
            messagebox.showinfo("完成", "所有數字都已到達終點！")
            return
        self.current += 1
        self.update_button_text()
        if self.current > self.end:
            messagebox.showinfo("完成", "所有數字都已到達終點！")

    def set_range(self):
        try:
            self.start = int(self.start_entry.get())
            self.end = int(self.end_entry.get())
            if self.start > self.end:
                raise ValueError("起始數字不能大於結束數字")
            self.current = self.start
            self.update_button_text()
            self.insert_button.config(state=tk.NORMAL)
        except ValueError as e:
            messagebox.showerror("錯誤", f"請輸入正確的數字範圍\n{e}")

    def update_button_text(self):
        if self.current <= self.end:
            self.insert_button.config(text=f"[～{self.current}]")
        else:
            self.insert_button.config(text="完成", state=tk.DISABLED)

    def insert_next(self):
        if self.current is None or self.current > self.end:
            messagebox.showinfo("完成", "所有數字都已插入完畢！")
            return
        text = f"[～{self.current}]"
        success, message = insert_bold_text_to_word(text)
        if success:
            self.current += 1
            self.update_button_text()
            if self.current > self.end:
                messagebox.showinfo("完成", "所有數字都已插入完畢！")
        else:
            messagebox.showerror("錯誤", message)

if __name__ == "__main__":
    root = tk.Tk()
    app = InsertRangeApp(root)
    root.mainloop()
