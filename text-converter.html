<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <title>文字轉換工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        textarea, p {
            width: 100%;
            max-width: 600px;
            margin-bottom: 10px;
        }
        button {
            padding: 10px 20px;
            background-color: #4CAF50;
            color: white;
            border: none;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
        #outputText {
            white-space: pre-wrap;
            background-color: #f5f5f5;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h2>文字轉換工具</h2>

    <div style="margin-bottom: 15px;">
        <label>
            <input type="checkbox" id="enableNumberRange" onchange="toggleNumberRange()">
            啟用數字範圍分段
        </label>
        <div id="numberRangeSettings" style="display: none; margin-top: 10px; padding: 10px; background-color: #f9f9f9; border-radius: 4px;">
            <div style="margin-bottom: 10px;">
                <label>
                    <input type="radio" name="rangeMode" value="range" checked>
                    數字範圍模式
                </label>
                <input type="text" id="numberRange" value="1-100" placeholder="例如：1-100" style="margin-left: 10px; padding: 5px;">
                <br><small style="color: #666;">只有在此範圍內的數字開頭才會被視為新段落</small>
            </div>
            <div>
                <label>
                    <input type="radio" name="rangeMode" value="sequence">
                    連續數字模式
                </label>
                <input type="text" id="sequenceRange" value="1-6" placeholder="例如：1-6" style="margin-left: 10px; padding: 5px;">
                <br><small style="color: #666;">從起始數字開始的連續序列（如設定1-6，有1,2,3就會分段1,2,3）</small>
            </div>
        </div>
    </div>

    <p>請輸入要轉換的文字（包含換行）：</p>
    <textarea id="inputText" rows="12" placeholder="輸入文字...">1第一個段落的內容，這裡有一些文字
和換行，還有一些數字如2023年、第344-50頁等。
2第二個段落開始了，這裡也有一些
內容和換行。
3第三個段落，包含更多內容
和一些參考資料，見頁239-241。
4第四個段落的內容
繼續在這裡。
999這個不應該被當作段落，因為不在1-6的連續序列中
5第五個段落
6最後一個段落，完成1-6的連續序列</textarea>
    <button onclick="convertText()">轉換文字</button>
    <h3>轉換結果：</h3>
    <p id="outputText"></p>

    <script>
        function toggleNumberRange() {
            const checkbox = document.getElementById("enableNumberRange");
            const settings = document.getElementById("numberRangeSettings");
            settings.style.display = checkbox.checked ? "block" : "none";
        }

        function isNumberInRange(number, rangeStr) {
            if (!rangeStr || rangeStr.trim() === '') return true;

            const parts = rangeStr.split('-');
            if (parts.length !== 2) return true;

            const min = parseInt(parts[0].trim());
            const max = parseInt(parts[1].trim());

            if (isNaN(min) || isNaN(max)) return true;

            return number >= min && number <= max;
        }

        function validateSequentialNumbers(lines) {
            // 提取所有以數字開頭的行的數字
            const numbers = [];

            for (let line of lines) {
                line = line.trim();
                if (line === '') continue;

                const numberMatch = line.match(/^(\d+)/);
                if (numberMatch) {
                    const number = parseInt(numberMatch[1]);
                    numbers.push(number);
                }
            }

            // 檢查連續序列範圍
            const sequenceRange = document.getElementById("sequenceRange").value;
            const parts = sequenceRange.split('-');
            if (parts.length !== 2) return new Set();

            const rangeStart = parseInt(parts[0].trim());
            const rangeEnd = parseInt(parts[1].trim());

            if (isNaN(rangeStart) || isNaN(rangeEnd)) return new Set();

            // 找出在範圍內的數字並排序
            const numbersInRange = numbers.filter(n => n >= rangeStart && n <= rangeEnd).sort((a, b) => a - b);

            // 去除重複數字
            const uniqueNumbers = [...new Set(numbersInRange)];

            if (uniqueNumbers.length === 0) return new Set();

            // 檢查是否從指定的起始數字開始連續
            const validNumbers = new Set();
            let expectedNext = rangeStart;

            for (let number of uniqueNumbers) {
                if (number === expectedNext) {
                    validNumbers.add(number);
                    expectedNext++;
                } else if (number > expectedNext) {
                    // 如果跳過了數字，停止驗證
                    break;
                }
            }

            return validNumbers;
        }

        function shouldStartNewParagraph(line, validSequenceNumbers = null) {
            const numberMatch = line.match(/^(\d+)/);
            if (!numberMatch) return false;

            const number = parseInt(numberMatch[1]);
            const enableRange = document.getElementById("enableNumberRange").checked;

            if (!enableRange) {
                // 如果沒有啟用範圍，使用更嚴格的規則
                // 只有當數字後面直接跟著中文字符或特定符號時才視為段落開始
                return /^\d+[^\d\-\s]/.test(line) || /^\d+$/.test(line.trim());
            }

            const rangeMode = document.querySelector('input[name="rangeMode"]:checked').value;

            if (rangeMode === 'sequence') {
                // 連續數字模式：只有在有效序列中的數字才能分段
                return validSequenceNumbers && validSequenceNumbers.has(number);
            } else {
                // 範圍模式：在範圍內的數字都可以分段
                const rangeStr = document.getElementById("numberRange").value;
                return isNumberInRange(number, rangeStr);
            }
        }

        function convertText() {
            // 取得輸入文字
            let input = document.getElementById("inputText").value;

            // 先按行分割
            let lines = input.split('\n');
            let result = [];
            let currentParagraph = '';

            // 如果啟用了連續數字模式，先驗證序列
            let validSequenceNumbers = null;
            const enableRange = document.getElementById("enableNumberRange").checked;
            if (enableRange) {
                const rangeMode = document.querySelector('input[name="rangeMode"]:checked').value;
                if (rangeMode === 'sequence') {
                    validSequenceNumbers = validateSequentialNumbers(lines);

                    // 如果沒有找到有效的連續序列，顯示提示
                    if (validSequenceNumbers.size === 0) {
                        const sequenceRange = document.getElementById("sequenceRange").value;
                        console.log(`提示：未找到從 ${sequenceRange.split('-')[0]} 開始的連續數字序列。`);
                    } else {
                        const validArray = Array.from(validSequenceNumbers).sort((a, b) => a - b);
                        console.log(`找到連續序列：${validArray.join(', ')}`);
                    }
                }
            }

            for (let line of lines) {
                line = line.trim();
                if (line === '') continue; // 跳過空行

                // 檢查是否應該開始新段落
                if (shouldStartNewParagraph(line, validSequenceNumbers)) {
                    // 如果當前段落不為空，先加入結果
                    if (currentParagraph) {
                        result.push(currentParagraph.trim());
                    }
                    // 開始新段落
                    currentParagraph = line;
                } else {
                    // 繼續當前段落
                    currentParagraph += line;
                }
            }

            // 加入最後一個段落
            if (currentParagraph) {
                result.push(currentParagraph.trim());
            }

            // 用換行符號連接各段落
            let output = result.join('\n\n');

            // 顯示結果
            document.getElementById("outputText").innerText = output;
        }
    </script>
</body>
</html>
