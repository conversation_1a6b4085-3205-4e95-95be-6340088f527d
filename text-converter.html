<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <title>文字轉換工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        textarea, p {
            width: 100%;
            max-width: 600px;
            margin-bottom: 10px;
        }
        button {
            padding: 10px 20px;
            background-color: #4CAF50;
            color: white;
            border: none;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
        #outputText {
            white-space: pre-wrap;
            background-color: #f5f5f5;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h2>文字轉換工具</h2>

    <div style="margin-bottom: 15px;">
        <label>
            <input type="checkbox" id="enableNumberRange" onchange="toggleNumberRange()">
            啟用數字範圍分段
        </label>
        <div id="numberRangeSettings" style="display: none; margin-top: 10px; padding: 10px; background-color: #f9f9f9; border-radius: 4px;">
            <label>數字範圍（例如：1-100）：</label>
            <input type="text" id="numberRange" value="1-100" placeholder="例如：1-100" style="margin-left: 10px; padding: 5px;">
            <br><small style="color: #666;">只有在此範圍內的數字開頭才會被視為新段落</small>
        </div>
    </div>

    <p>請輸入要轉換的文字（包含換行）：</p>
    <textarea id="inputText" rows="10" placeholder="輸入文字...">
71此模型定義與在法學領域的運用 See ROBERT M. LAWLESS ET AL., EMPIRICAL 
METHODS IN LAW 344-50 (2009). 運用此模型研究臺灣法律問題，見：張永健，
分割共有物判決之實證研究，收於：張永健編，2011司法制度實證研究，頁
239-241（2013年）；張永健，越界建築訴訟之實證研究，中研院法學期刊，14
期，頁357-363（2014年）。
72因我國刑事案件是由「被告」與「犯罪事實」構成，數被告共同為一個犯罪事
實時，會被評價為數案件。因此，雖然修法後論及文化抗辯的案件數為39個，
但因存在數被告共同獵捕野生動物的共犯案件，故實際上僅有25個獵捕野生動
物的犯罪事實。考慮法官評價數被告所為的一個獵捕犯罪事實時，作為評價基礎的獵捕動物種類、數量與獵捕目的是相同的，對該事實的評價並不會因不同
被告而有異，故宜以25個犯罪事實為迴歸分析之樣本。
    </textarea>
    <button onclick="convertText()">轉換文字</button>
    <h3>轉換結果：</h3>
    <p id="outputText"></p>

    <script>
        function toggleNumberRange() {
            const checkbox = document.getElementById("enableNumberRange");
            const settings = document.getElementById("numberRangeSettings");
            settings.style.display = checkbox.checked ? "block" : "none";
        }

        function isNumberInRange(number, rangeStr) {
            if (!rangeStr || rangeStr.trim() === '') return true;

            const parts = rangeStr.split('-');
            if (parts.length !== 2) return true;

            const min = parseInt(parts[0].trim());
            const max = parseInt(parts[1].trim());

            if (isNaN(min) || isNaN(max)) return true;

            return number >= min && number <= max;
        }

        function shouldStartNewParagraph(line) {
            const numberMatch = line.match(/^(\d+)/);
            if (!numberMatch) return false;

            const number = parseInt(numberMatch[1]);
            const enableRange = document.getElementById("enableNumberRange").checked;

            if (!enableRange) {
                // 如果沒有啟用範圍，使用更嚴格的規則
                // 只有當數字後面直接跟著中文字符或特定符號時才視為段落開始
                return /^\d+[^\d\-\s]/.test(line) || /^\d+$/.test(line.trim());
            }

            const rangeStr = document.getElementById("numberRange").value;
            return isNumberInRange(number, rangeStr);
        }

        function convertText() {
            // 取得輸入文字
            let input = document.getElementById("inputText").value;

            // 先按行分割
            let lines = input.split('\n');
            let result = [];
            let currentParagraph = '';

            for (let line of lines) {
                line = line.trim();
                if (line === '') continue; // 跳過空行

                // 檢查是否應該開始新段落
                if (shouldStartNewParagraph(line)) {
                    // 如果當前段落不為空，先加入結果
                    if (currentParagraph) {
                        result.push(currentParagraph.trim());
                    }
                    // 開始新段落
                    currentParagraph = line;
                } else {
                    // 繼續當前段落
                    currentParagraph += line;
                }
            }

            // 加入最後一個段落
            if (currentParagraph) {
                result.push(currentParagraph.trim());
            }

            // 用換行符號連接各段落
            let output = result.join('\n\n');

            // 顯示結果
            document.getElementById("outputText").innerText = output;
        }
    </script>
</body>
</html>
