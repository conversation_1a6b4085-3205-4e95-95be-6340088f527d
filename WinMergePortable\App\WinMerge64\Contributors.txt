
People who have contributed to WinM<PERSON>ge
---------------------------------------

Original developer, project admin:
* <PERSON> <<EMAIL>>

Project lead:
* <PERSON> <<EMAIL>>

Developers:
* <PERSON> <denis<PERSON><EMAIL>>
* <PERSON> <<EMAIL>>
* <PERSON> <<EMAIL>>
* <PERSON><PERSON> <<EMAIL>>
* <PERSON><PERSON><PERSON> <<EMAIL>>
* <PERSON> (Graphic Design) <<EMAIL>>
* <PERSON><PERSON> <<EMAIL>>

Inactive/past developers:
* <PERSON>
* <PERSON>
* <PERSON>
* <PERSON>
* <PERSON> "<PERSON><PERSON>" <PERSON> (Installer)
* <PERSON><PERSON> <<EMAIL>>

Localization:
* Arabic:
  Downzen team <https://downzen.com>

* Basque:
  <PERSON><PERSON><PERSON> <A<PERSON><EMAIL>>  

* Bulgarian:
  Sld <sld|mail.bg>
  tigertron <<EMAIL>>
  <PERSON><PERSON> <yanko<PERSON> at hotmail.com>
  Стоян <stoyan от гмаил>

* Catalan:
  <PERSON><PERSON> <j<PERSON><EMAIL>>
  Domènec <domenec72 at gmail.com>

* Simplified Chinese:
  Liaobin <liaobin@jite. net>
  xmpdhml <xmpdhml at users.sourceforge.net>
  MaysWind <i at mayswind.net>
  YFdyh000 <yfdyh000 at gmail.com>

* Traditional Chinese:
  Koko <<EMAIL>>
  Calvin Lin <ylin77 at gmail.com>

* Croatian:
  Hasan Osmanagiæ <<EMAIL>>

* Czech:
  Jiri Tax <<EMAIL>>
  Jan Hryz <<EMAIL>>

* Danish:
  Rolf Egmose
  Christian List <<EMAIL>>

* Dutch:
  Michel Coene
  Thomas De Rocker <<EMAIL>>
  Ronald Schaap <<EMAIL>>

* Finnish:
  Veikko Muurikainen <veikko.muurikainen at kolumbus.fi>

* French:
  Jean-F Jolin <<EMAIL>>
  Gil Andre <<EMAIL>>
  Laurent Ganier <<EMAIL>>
  Dominique Saussereau <<EMAIL>>
  Matthieu Pupat
  gandf
  Lolo S. <slolo2000 at hotmail.com>

* Galician:
  Medulio
  Luis A. Martínez Sobrino <luis.martinez.sobrino at gmail.com>

* German:
  Tim Gerundt <<EMAIL>>
  Winfried Peter <<EMAIL>>
  Joerg Schneider <<EMAIL>>
  Mr-Update

* Greek:
  Polyvios J. Simopoulos <<EMAIL>>

* Hungarian:
  Márton Balázs <<EMAIL>>
  Mihalicza József <<EMAIL>>
  Egyed Ferenc <efi99 at freemail.hu>

* Italian:
  Andrea Decorte <<EMAIL>>
  Antonio Angelo <<EMAIL>>
  Michele Merega <<EMAIL>>
  Michele Locati <<EMAIL>>
  bovirus
  Massimiliano Caniparoli

* Korean:
  Sukjoon <<EMAIL>>
  Lee Jae-Hong <<EMAIL>>
  Sushizang <<EMAIL>>
  sheppaul <sheppaul at gmail.com>
  teamzamong <teamzamong at gmail.com>
  BrainS <29201475+BraINstinct0 at users.noreply.github.com>

* Lithuanian:
  Dalius Guzauskas (aka Tichij) <tichij AT mail DOT com>

* Norwegian:
  Hans Fredrik Nordhaug <<EMAIL>>

* Persian:
  Abolfazl Rostamzadeh <a.rostamzadeh at gmail.com>

* Polish:
  M.T <<EMAIL>>
  Skiff <<EMAIL>>
  Pawel Wawrzysko <<EMAIL>>
  Mirosław Żylewicz
  Michał Lipok

* Portuguese (Brazilian):
  Wender Firmino <<EMAIL>>
  Felipe Periard Lopes <<EMAIL>>
  Igor Rückert
  jota11
  Gabriel Camargo Fukushima

* Portuguese (Portugal):
  Nelson Simão
  Lippe35
  Hugo Carvalho <<EMAIL>>

* Romanian:
  Cristian Arghiroiu <<EMAIL>>

* Russian:
  Dmitriy S. Aleshkowskiy <<EMAIL>>
  Timon34
  wvxwxvw

* Serbian:
  Ozzii <<EMAIL>>

* Sinhala:
  T G E Perera

* Slovak:
  Majvan <<EMAIL>>
  Ivan Masár <<EMAIL>>
  Jozef Matta <<EMAIL>>

* Slovenian:
  Iztok Osredkar <<EMAIL>>
  Filip Komar <<EMAIL>>
  Jadran Rudec <<EMAIL>>

* Spanish:
  Dean Grimm <<EMAIL>>
  Jesús M. Delgado 'MacK' <<EMAIL>>
  Mario Angel <<EMAIL>>
  Nelson Ariza <<EMAIL>>
  borjafg

* Swedish:
  Hans Eriksson <<EMAIL>>
  Göran Håkansson <<EMAIL>>
  pgert <pgert at yahoo.se>
  Timmy Almroth <timmy.almroth at tim-international.net>

* Turkish:
  Afyonlu <<EMAIL>>
  Ozkan UNVER <<EMAIL>>
  Kaya Zeren <kaya.zeren at zeron.net>

* Ukranian
  Vitaliy Stopchans'kyy <<EMAIL>>
  Warrior <<EMAIL>>


Other Contributors (code, ideas, testing..):
* James Abbatiello <<EMAIL>>
* Andre Arpin
* Steve Beaudoin
* bulklodd <<EMAIL>>
* Adam Carrivick
* Hern Chen
* Jeremy Dewey
* Jim Fougeron
* Marvin Gouw
* Robert Grange
* black hero <<EMAIL>>
* Jan Hryz
* jwdevel
* Ed_K
* Alberto Kalb
* Joe Kidd <<EMAIL>>
* Robert Knienider
* Ferenc Kubatovics
* René Leonhardt
* Vladimir Lukianov
* LynX <<EMAIL>>
* David Maisonave
* Dmitry Maslov
* Denis Matiukha
* Matthias Mayer
* Harry Mckame
* Oliver Mellet
* Andreas Morsing
* Ryan Mott
* Tim Musschoot
* Marco De Paoli
* Paul <<EMAIL>>
* Chris Paulse <<EMAIL>>
* Dan Pidcock
* Vincent Osele
* Scott Rasmussen
* Michael Richter
* Markus Rollmann
* saitaman <<EMAIL>>
* Philippe Verdy
* Vikrant
* Gilbert Wellisch
* Paul Welter
* Lars Wittenburg
* Etienne Faisant
* Bill Gord
* H.Saido
* Flaviu_
* Jun Tajima
* EugeneLaptev
* ranjwarrior
* elsonwei

WinMerge includes code from:
* Jared Breland <<EMAIL>> (Installer's modpath script)
* Brent Corkum (BCMenu)
* Paul DiLascia (CStaticLink) 
* Michael Dunn (CShellFileOp)
* Francis Irving (original conflict file parser)
* Michael P. Mehl (CMessageBoxDialog)
* Cristi Posea (CSizingControlBar)
* Ferdinand Prantl (Crystal Edit syntax rules)
* Keith Rule (CMemDC - memory DC)
* Paul Senzee (String formatting)
* Henry Spencer (CRegExp)
* Andrei Stcherbatchenko (Author of Crystal Edit)
* Sven Wiegand (Crystal Edit)

*** NOTE ***
If your name is missing please let us know (or create a pull request).
https://github.com/WinMerge/winmerge

Thanks!
