# This file is part from WinMerge <https://winmerge.org/>
# Released under the "GNU General Public License"
#
# Translators:
# <AUTHOR> <EMAIL>
#
# ID line follows -- this is updated by SVN
# $Id$
#
msgid ""
msgstr ""
"Project-Id-Version: WinMerge Shell Extension\n"
"Report-Msgid-Bugs-To: https://bugs.winmerge.org/\n"
"POT-Creation-Date: 2021-09-08 23:40+0000\n"
"PO-Revision-Date: 2021-09-21 19:06+0300\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Turkish <<EMAIL>>\n"
"Language: tr\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-Basepath: ../../ShellExtension/Languages\n"
"X-Generator: Poedit 3.0\n"

#. AFX_TARG_*
#: ShellExtension.rc:19
#, c-format
msgid "AFX_TARG_ENU"
msgstr "AFX_TARG_TRK"

#. LANGUAGE, SUBLANGUAGE
#: ShellExtension.rc:20
#, c-format
msgid "LANG_ENGLISH, SUBLANG_ENGLISH_US"
msgstr "LANG_TURKISH, SUBLANG_DEFAULT"

#. Codepage
#: ShellExtension.rc:21
#, c-format
msgid "1252"
msgstr "65001"

#: ShellExtension.rc:111
#, c-format
msgid "ShellExtension"
msgstr "KabukEklentisi"

#| msgid "WinMerge"
#: ShellExtension.rc:112
#, c-format
msgid "&WinMerge"
msgstr "&WinMerge"

#: ShellExtension.rc:113
#, c-format
msgid "&Compare"
msgstr "&Karşılaştır"

#: ShellExtension.rc:114
#, c-format
msgid "Compare&..."
msgstr "Karşılaştı&r..."

#: ShellExtension.rc:115
#, c-format
msgid "Select &Left"
msgstr "So&lu seç"

#: ShellExtension.rc:116
#, c-format
msgid "Select &Middle"
msgstr "&Ortayı seç"

#: ShellExtension.rc:117
#, c-format
msgid "Re-select &Left"
msgstr "So&lu yeniden seç"
