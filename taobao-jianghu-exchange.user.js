// ==UserScript==
// @name         淘江湖兌換優化版
// @namespace    http://tampermonkey.net/
// @version      2.0
// @description  Automatically click the specified reward button repeatedly when it shows "立即兑换", reload if "已兑完", with pause/resume UI and persistent state. Optimized for continuous clicking.
// <AUTHOR>
// @match        https://jianghu.taobao.com/coin.html
// @grant        GM_addStyle
// @grant        GM_getValue
// @grant        GM_setValue
// ==/UserScript==

(function() {
    'use strict';

    // 設置變數
    let isRunning = GM_getValue('isRunning', false);
    let targetIndex = GM_getValue('targetIndex', 0);
    let checkInterval = null;
    let continuousClickMode = false; // 新增：連續點擊模式標記
    let clickCount = 0; // 新增：點擊計數器

    // 創建UI
    function createUI() {
        const ui = document.createElement('div');
        ui.id = 'auto-click-ui';
        ui.innerHTML = `
            <div style="position: fixed; top: 10px; right: 10px; background: #fff; border: 1px solid #ccc; padding: 10px; z-index: 9999; border-radius: 5px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                <h3 style="margin: 0 0 10px 0; color: #333;">淘江湖自動兌換</h3>
                <div style="margin-bottom: 10px;">
                    <label>目標按鈕索引 (從0開始):</label>
                    <input type="number" id="targetIndex" value="${targetIndex}" min="0" style="width: 60px; margin: 5px; padding: 3px;">
                </div>
                <div style="margin-bottom: 10px;">
                    <button id="startBtn" style="background: #4CAF50; color: white; border: none; padding: 8px 12px; border-radius: 3px; cursor: pointer;">開始</button>
                    <button id="pauseBtn" disabled style="background: #f44336; color: white; border: none; padding: 8px 12px; border-radius: 3px; cursor: pointer;">暫停</button>
                </div>
                <div style="font-size: 12px; color: #666;">
                    <p style="margin: 5px 0;">狀態: <span id="status" style="font-weight: bold;">已停止</span></p>
                    <p style="margin: 5px 0;">模式: <span id="mode">檢查模式</span></p>
                    <p style="margin: 5px 0;">點擊次數: <span id="clickCount">0</span></p>
                </div>
            </div>
        `;
        document.body.appendChild(ui);

        // 添加樣式
        GM_addStyle(`
            #auto-click-ui { font-family: Arial, sans-serif; }
            #auto-click-ui button:disabled { 
                background: #ccc !important; 
                cursor: not-allowed !important; 
            }
            #auto-click-ui button:not(:disabled):hover {
                opacity: 0.8;
            }
        `);

        // 綁定事件
        document.getElementById('startBtn').addEventListener('click', startScript);
        document.getElementById('pauseBtn').addEventListener('click', pauseScript);
        document.getElementById('targetIndex').addEventListener('change', (e) => {
            targetIndex = parseInt(e.target.value, 10) || 0;
            GM_setValue('targetIndex', targetIndex);
        });

        // 初始化按鈕狀態
        updateButtonStates();
    }

    // 更新按鈕狀態
    function updateButtonStates() {
        document.getElementById('startBtn').disabled = isRunning;
        document.getElementById('pauseBtn').disabled = !isRunning;
        updateStatus(isRunning ? '運行中...' : '已停止');
        updateMode(continuousClickMode ? '連續點擊模式' : '檢查模式');
        updateClickCount(clickCount);
    }

    // 更新狀態顯示
    function updateStatus(text) {
        const statusElement = document.getElementById('status');
        if (statusElement) {
            statusElement.textContent = text;
            statusElement.style.color = isRunning ? '#4CAF50' : '#666';
        }
    }

    // 更新模式顯示
    function updateMode(text) {
        const modeElement = document.getElementById('mode');
        if (modeElement) {
            modeElement.textContent = text;
            modeElement.style.color = continuousClickMode ? '#ff9800' : '#666';
        }
    }

    // 更新點擊計數顯示
    function updateClickCount(count) {
        const countElement = document.getElementById('clickCount');
        if (countElement) {
            countElement.textContent = count;
        }
    }

    // 開始腳本
    function startScript() {
        if (isRunning) return;
        isRunning = true;
        continuousClickMode = false;
        clickCount = 0;
        GM_setValue('isRunning', isRunning);
        targetIndex = parseInt(document.getElementById('targetIndex').value, 10) || 0;
        GM_setValue('targetIndex', targetIndex);
        updateButtonStates();
        checkButton();
    }

    // 暫停腳本
    function pauseScript() {
        isRunning = false;
        continuousClickMode = false;
        GM_setValue('isRunning', isRunning);
        clearInterval(checkInterval);
        updateButtonStates();
        updateStatus('已暫停');
    }



    // 檢查並點擊按鈕（優化版）
    function checkButton() {
        if (!isRunning) return;

        const buttons = document.querySelectorAll('.reward-item-btn.reward-receive');
        if (buttons.length === 0 || targetIndex >= buttons.length) {
            updateStatus(`未找到按鈕或索引 ${targetIndex} 無效，等待重整...`);
            setTimeout(() => location.reload(), 2000);
            return;
        }

        const targetButton = buttons[targetIndex];
        const buttonText = targetButton.textContent;

        if (buttonText.includes('立即兑换')) {
            if (!continuousClickMode) {
                // 首次發現立即兑换，進入連續點擊模式
                continuousClickMode = true;
                updateMode('連續點擊模式');
                updateStatus('進入連續點擊模式，持續點擊中...');
            }
            
            // 連續點擊立即兑换按鈕
            targetButton.click();
            clickCount++;
            updateClickCount(clickCount);
            
            // 檢查確認按鈕
            setTimeout(checkConfirmButton, 300);
            
            // 在連續點擊模式下，更頻繁地點擊（每500ms一次）
            checkInterval = setTimeout(checkButton, 500);
            
        } else if (buttonText.includes('已兑完')) {
            updateStatus(`索引 ${targetIndex} 已兑完，重新整理頁面...`);
            continuousClickMode = false;
            setTimeout(() => location.reload(), 1000);
            
        } else {
            // 如果之前在連續點擊模式，但現在按鈕狀態改變了
            if (continuousClickMode) {
                updateStatus('按鈕狀態改變，退出連續點擊模式');
                continuousClickMode = false;
                updateMode('檢查模式');
            } else {
                updateStatus(`索引 ${targetIndex} 按鈕狀態: ${buttonText}，等待重試...`);
            }
            
            // 正常檢查間隔（每2秒一次）
            checkInterval = setTimeout(checkButton, 2000);
        }
    }

    // 檢查並點擊確認按鈕（優化版）
    function checkConfirmButton() {
        if (!isRunning) return;
        
        const confirmButton = document.querySelector('.dialog-btn-confirm');
        if (confirmButton && confirmButton.offsetParent !== null) { // 確保按鈕可見
            confirmButton.click();
            clickCount++;
            updateClickCount(clickCount);
            updateStatus('確認按鈕已點擊，繼續連續點擊...');
        }
        // 不管是否找到確認按鈕都繼續，因為有些情況下確認框可能很快消失
    }

    // 初始化
    createUI();
    
    // 檢查保存的狀態，自動恢復運行
    if (isRunning) {
        updateButtonStates();
        checkButton();
    }

    // 頁面卸載時保存狀態
    window.addEventListener('beforeunload', () => {
        GM_setValue('isRunning', false); // 頁面刷新時停止運行
    });

})();
