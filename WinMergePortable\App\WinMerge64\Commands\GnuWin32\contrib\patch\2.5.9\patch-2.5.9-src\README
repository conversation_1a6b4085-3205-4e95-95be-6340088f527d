This version of `patch' has many changes made by the Free Software Foundation.
They add support for:
 * handling arbitrary binary data and large files
 * the unified context diff format that GNU diff can produce
 * making GNU Emacs-style backup files
 * improved interaction with RCS and SCCS
 * the GNU conventions for option parsing and configuring and compilation.
 * better POSIX compliance
They also fix some bugs.  See the NEWS and ChangeLog files for details.

Tutorial-style documentation for patch is included in the GNU
Diffutils package; get GNU Diffutils 2.8 or later for up-to-date
documentation for patch.

For GNU and Unix build and installation instructions, see the file INSTALL.
Use `configure --disable-largefile' to disable large file support;
this is reportedly necessary on Red Hat GNU/Linux 6.0 to avoid a C library bug.
For MS-DOS using DJGPP tools, see the file pc/djgpp/README.
For other systems, copy config.hin to config.h and change
#undef statements in it to #define as appropriate for your system,
and copy Makefile.in to Makefile and set the variables that are
enclosed in @ signs as appropriate for your system.

Please send bug reports for this version of patch to
<<EMAIL>>.

The Free Software Foundation is distributing this version of patch
independently because as of this writing, <PERSON> has not released a
new version of patch since mid-1988.  We have heard that he has been
too busy working on other things, like <PERSON><PERSON>.  He has graciously agreed
to let GNU `patch' be distributed under the terms of the GNU General
Public License.

------

Copyright (C) 1984, 1985, 1986, 1987, 1988 Larry Wall

Copyright (C) 1989, 1990, 1991, 1992, 1993, 1997, 1999, 2002 Free
Software Foundation, Inc.

This program is free software: you can redistribute it and/or modify
it under the terms of the GNU General Public License as published by
the Free Software Foundation; either version 2, or (at your option)
any later version.

This program is distributed in the hope that it will be useful,
but WITHOUT ANY WARRANTY; without even the implied warranty of
MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.
See the GNU General Public License for more details.

You should have received a copy of the GNU General Public License
along with this file; see the file COPYING.
If not, write to the Free Software Foundation,
59 Temple Place - Suite 330, Boston, MA 02111-1307, USA.
