#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
測試文字框滾動限制功能
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os

# 添加當前目錄到 Python 路徑
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_textbox_scroll_restrictions():
    """測試文字框滾動限制功能"""
    try:
        # 導入 PDF 閱讀器
        from importlib import import_module
        pdf_reader_module = import_module('20250622閱讀器')
        
        # 創建測試窗口
        root = tk.Tk()
        root.title("文字框滾動限制功能測試")
        root.geometry("800x600")
        
        # 創建 PDF 閱讀器實例
        app = pdf_reader_module.PDFReader(root)
        
        # 檢查是否有 toggle_textbox_scroll 方法
        if hasattr(app, 'toggle_textbox_scroll'):
            print("✓ toggle_textbox_scroll 方法存在")
        else:
            print("✗ toggle_textbox_scroll 方法不存在")
            return False
            
        # 檢查是否有 _disable_textbox_interactions 方法
        if hasattr(app, '_disable_textbox_interactions'):
            print("✓ _disable_textbox_interactions 方法存在")
        else:
            print("✗ _disable_textbox_interactions 方法不存在")
            return False
            
        # 檢查是否有 _enable_textbox_interactions 方法
        if hasattr(app, '_enable_textbox_interactions'):
            print("✓ _enable_textbox_interactions 方法存在")
        else:
            print("✗ _enable_textbox_interactions 方法不存在")
            return False
            
        # 檢查初始狀態
        if hasattr(app, 'textbox_scroll_enabled'):
            print(f"✓ textbox_scroll_enabled 初始值: {app.textbox_scroll_enabled}")
        else:
            print("✗ textbox_scroll_enabled 屬性不存在")
            return False
            
        # 測試切換功能
        initial_state = app.textbox_scroll_enabled
        app.toggle_textbox_scroll()
        new_state = app.textbox_scroll_enabled
        
        if new_state != initial_state:
            print("✓ toggle_textbox_scroll 功能正常")
        else:
            print("✗ toggle_textbox_scroll 功能異常")
            return False
            
        print("所有測試通過！")
        
        # 顯示測試結果
        messagebox.showinfo("測試結果", 
                          "文字框滾動限制功能測試通過！\n\n"
                          "新增功能：\n"
                          "1. 禁用文字選取\n"
                          "2. 清除待處理的 after 調用\n"
                          "3. 強化滾動位置保護\n"
                          "4. 阻止導航按鍵觸發滾動")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"測試失敗: {e}")
        if 'root' in locals():
            root.destroy()
        return False

if __name__ == "__main__":
    success = test_textbox_scroll_restrictions()
    if success:
        print("\n測試完成：功能正常")
    else:
        print("\n測試失敗：請檢查代碼")
