# This file is part from WinMerge <https://winmerge.org/>
# Released under the "GNU General Public License"
#
# ID line follows -- this is updated by SVN
# $Id$
#
msgid ""
msgstr ""
"Project-Id-Version: WinMerge Shell Extension\n"
"Report-Msgid-Bugs-To: https://bugs.winmerge.org/\n"
"POT-Creation-Date: 2009-07-26 14:36+0000\n"
"PO-Revision-Date: \n"
"Last-Translator: \n"
"Language-Team: Serbian <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-Basepath: ../../ShellExtension/Languages\n"
"Language: sr\n"
"X-Generator: Poedit 2.1.1\n"

#. AFX_TARG_*
#: ShellExtension.rc:19
#, c-format
msgid "AFX_TARG_ENU"
msgstr "AFX_TARG_SER"

#. LANGUAGE, SUBLANGUAGE
#: ShellExtension.rc:20
#, c-format
msgid "LANG_ENGLISH, SUBLANG_ENGLISH_US"
msgstr "LANG_SERBIAN, SUBLANG_SERBIAN_CYRILLIC"

#. Codepage
#: ShellExtension.rc:21
#, c-format
msgid "1252"
msgstr "65001"

#: ShellExtension.rc:111
#, c-format
msgid "ShellExtension"
msgstr "ShellExtension"

#: ShellExtension.rc:112
#, c-format
msgid "&WinMerge"
msgstr ""

#: ShellExtension.rc:113
#, c-format
msgid "&Compare"
msgstr ""

#: ShellExtension.rc:114
#, c-format
msgid "Compare&..."
msgstr ""

#: ShellExtension.rc:115
#, c-format
msgid "Select &Left"
msgstr ""

#: ShellExtension.rc:116
#, c-format
msgid "Select &Middle"
msgstr ""

#: ShellExtension.rc:117
#, c-format
msgid "Re-select &Left"
msgstr ""
