<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang xml:lang>
<head>
  <meta charset="utf-8" />
  <meta name="generator" content="pandoc" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes" />
  <title>ReleaseNotes</title>
  <style>
    code{white-space: pre-wrap;}
    span.smallcaps{font-variant: small-caps;}
    span.underline{text-decoration: underline;}
    div.column{display: inline-block; vertical-align: top; width: 50%;}
    div.hanging-indent{margin-left: 1.5em; text-indent: -1.5em;}
    ul.task-list{list-style: none;}
    .display.math{display: block; text-align: center; margin: 0.5rem auto;}
  </style>
  <link rel="stylesheet" href="data:text/css,article%2Caside%2Cdetails%2Cfigcaption%2Cfigure%2Cfooter%2Cheader%2Chgroup%2Cmain%2Cnav%2Csection%2Csummary%20%7Bdisplay%3A%20block%3B%7Daudio%2Ccanvas%2Cvideo%20%7Bdisplay%3A%20inline%2Dblock%3B%7Daudio%3Anot%28%5Bcontrols%5D%29%20%7Bdisplay%3A%20none%3Bheight%3A%200%3B%7D%5Bhidden%5D%2Ctemplate%20%7Bdisplay%3A%20none%3B%7Dhtml%20%7Bfont%2Dfamily%3A%20sans%2Dserif%3B%20%2Dms%2Dtext%2Dsize%2Dadjust%3A%20100%25%3B%20%2Dwebkit%2Dtext%2Dsize%2Dadjust%3A%20100%25%3B%20%7Dbody%20%7Bmargin%3A%200%3B%7Da%20%7Bbackground%3A%20transparent%3B%7Da%3Afocus%20%7Boutline%3A%20thin%20dotted%3B%7Da%3Aactive%2Ca%3Ahover%20%7Boutline%3A%200%3B%7Dh1%20%7Bfont%2Dsize%3A%202em%3Bmargin%3A%200%2E67em%200%3B%7Dabbr%5Btitle%5D%20%7Bborder%2Dbottom%3A%201px%20dotted%3B%7Db%2Cstrong%20%7Bfont%2Dweight%3A%20bold%3B%7Ddfn%20%7Bfont%2Dstyle%3A%20italic%3B%7Dhr%20%7B%2Dmoz%2Dbox%2Dsizing%3A%20content%2Dbox%3Bbox%2Dsizing%3A%20content%2Dbox%3Bheight%3A%200%3B%7Dmark%20%7Bbackground%3A%20%23ff0%3Bcolor%3A%20%23000%3B%7Dcode%2Ckbd%2Cpre%2Csamp%20%7Bfont%2Dfamily%3A%20monospace%2C%20serif%3Bfont%2Dsize%3A%201em%3B%7Dpre%20%7Bwhite%2Dspace%3A%20pre%2Dwrap%3B%7Dq%20%7Bquotes%3A%20%22%5C201C%22%20%22%5C201D%22%20%22%5C2018%22%20%22%5C2019%22%3B%7Dsmall%20%7Bfont%2Dsize%3A%2080%25%3B%7Dsub%2Csup%20%7Bfont%2Dsize%3A%2075%25%3Bline%2Dheight%3A%200%3Bposition%3A%20relative%3Bvertical%2Dalign%3A%20baseline%3B%7Dsup%20%7Btop%3A%20%2D0%2E5em%3B%7Dsub%20%7Bbottom%3A%20%2D0%2E25em%3B%7Dimg%20%7Bborder%3A%200%3B%7Dsvg%3Anot%28%3Aroot%29%20%7Boverflow%3A%20hidden%3B%7Dfigure%20%7Bmargin%3A%200%3B%7Dfieldset%20%7Bborder%3A%201px%20solid%20%23c0c0c0%3Bmargin%3A%200%202px%3Bpadding%3A%200%2E35em%200%2E625em%200%2E75em%3B%7Dlegend%20%7Bborder%3A%200%3B%20padding%3A%200%3B%20%7Dbutton%2Cinput%2Cselect%2Ctextarea%20%7Bfont%2Dfamily%3A%20inherit%3B%20font%2Dsize%3A%20100%25%3B%20margin%3A%200%3B%20%7Dbutton%2Cinput%20%7Bline%2Dheight%3A%20normal%3B%7Dbutton%2Cselect%20%7Btext%2Dtransform%3A%20none%3B%7Dbutton%2Chtml%20input%5Btype%3D%22button%22%5D%2C%20input%5Btype%3D%22reset%22%5D%2Cinput%5Btype%3D%22submit%22%5D%20%7B%2Dwebkit%2Dappearance%3A%20button%3B%20cursor%3A%20pointer%3B%20%7Dbutton%5Bdisabled%5D%2Chtml%20input%5Bdisabled%5D%20%7Bcursor%3A%20default%3B%7Dinput%5Btype%3D%22checkbox%22%5D%2Cinput%5Btype%3D%22radio%22%5D%20%7Bbox%2Dsizing%3A%20border%2Dbox%3B%20padding%3A%200%3B%20%7Dinput%5Btype%3D%22search%22%5D%20%7B%2Dwebkit%2Dappearance%3A%20textfield%3B%20%2Dmoz%2Dbox%2Dsizing%3A%20content%2Dbox%3B%2Dwebkit%2Dbox%2Dsizing%3A%20content%2Dbox%3B%20box%2Dsizing%3A%20content%2Dbox%3B%7Dinput%5Btype%3D%22search%22%5D%3A%3A%2Dwebkit%2Dsearch%2Dcancel%2Dbutton%2Cinput%5Btype%3D%22search%22%5D%3A%3A%2Dwebkit%2Dsearch%2Ddecoration%20%7B%2Dwebkit%2Dappearance%3A%20none%3B%7Dbutton%3A%3A%2Dmoz%2Dfocus%2Dinner%2Cinput%3A%3A%2Dmoz%2Dfocus%2Dinner%20%7Bborder%3A%200%3Bpadding%3A%200%3B%7Dtextarea%20%7Boverflow%3A%20auto%3B%20vertical%2Dalign%3A%20top%3B%20%7Dtable%20%7Bborder%2Dcollapse%3A%20collapse%3Bborder%2Dspacing%3A%200%3B%7D%2Ego%2Dtop%20%7Bposition%3A%20fixed%3Bbottom%3A%202em%3Bright%3A%202em%3Btext%2Ddecoration%3A%20none%3Bbackground%2Dcolor%3A%20%23E0E0E0%3Bfont%2Dsize%3A%2012px%3Bpadding%3A%201em%3Bdisplay%3A%20inline%3B%7Dhtml%2Cbody%7B%20margin%3A%20auto%3Bpadding%2Dright%3A%201em%3Bpadding%2Dleft%3A%201em%3Bmax%2Dwidth%3A%2044em%3B%20color%3Ablack%3B%7D%2A%3Anot%28%27%23mkdbuttons%27%29%7Bmargin%3A0%3Bpadding%3A0%7Dbody%7Bfont%3A13%2E34px%20helvetica%2Carial%2Cfreesans%2Cclean%2Csans%2Dserif%3B%2Dwebkit%2Dfont%2Dsmoothing%3Asubpixel%2Dantialiased%3Bline%2Dheight%3A1%2E4%3Bpadding%3A3px%3Bbackground%3A%23fff%3Bborder%2Dradius%3A3px%3B%2Dmoz%2Dborder%2Dradius%3A3px%3B%2Dwebkit%2Dborder%2Dradius%3A3px%7Dp%7Bmargin%3A1em%200%7Da%7Bcolor%3A%234183c4%3Btext%2Ddecoration%3Anone%7Dbody%7Bbackground%2Dcolor%3A%23fff%3Bpadding%3A30px%3Bmargin%3A15px%3Bfont%2Dsize%3A14px%3Bline%2Dheight%3A1%2E6%7Dbody%3E%2A%3Afirst%2Dchild%7Bmargin%2Dtop%3A0%21important%7Dbody%3E%2A%3Alast%2Dchild%7Bmargin%2Dbottom%3A0%21important%7D%40media%20screen%7Bbody%7Bbox%2Dshadow%3A0%200%200%201px%20%23cacaca%2C0%200%200%204px%20%23eee%7D%7Dh1%2Ch2%2Ch3%2Ch4%2Ch5%2Ch6%7Bmargin%3A20px%200%2010px%3Bpadding%3A0%3Bfont%2Dweight%3Abold%3B%2Dwebkit%2Dfont%2Dsmoothing%3Asubpixel%2Dantialiased%3Bcursor%3Atext%7Dh1%7Bfont%2Dsize%3A28px%3Bcolor%3A%23000%7Dh2%7Bfont%2Dsize%3A24px%3Bborder%2Dbottom%3A1px%20solid%20%23ccc%3Bcolor%3A%23000%7Dh3%7Bfont%2Dsize%3A18px%3Bcolor%3A%23333%7Dh4%7Bfont%2Dsize%3A16px%3Bcolor%3A%23333%7Dh5%7Bfont%2Dsize%3A14px%3Bcolor%3A%23333%7Dh6%7Bcolor%3A%23777%3Bfont%2Dsize%3A14px%7Dp%2Cblockquote%2Ctable%2Cpre%7Bmargin%3A15px%200%7Dul%7Bpadding%2Dleft%3A30px%7Dol%7Bpadding%2Dleft%3A30px%7Dol%20li%20ul%3Afirst%2Dof%2Dtype%7Bmargin%2Dtop%3A0%7Dhr%7Bbackground%3Atransparent%20url%28data%3Aimage%2Fpng%3Bbase64%2CiVBORw0KGgoAAAANSUhEUgAAAAYAAAAECAYAAACtBE5DAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyJpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw%2FeHBhY2tldCBiZWdpbj0i77u%2FIiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8%2BIDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuMC1jMDYwIDYxLjEzNDc3NywgMjAxMC8wMi8xMi0xNzozMjowMCAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENTNSBNYWNpbnRvc2giIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6OENDRjNBN0E2NTZBMTFFMEI3QjRBODM4NzJDMjlGNDgiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6OENDRjNBN0I2NTZBMTFFMEI3QjRBODM4NzJDMjlGNDgiPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDo4Q0NGM0E3ODY1NkExMUUwQjdCNEE4Mzg3MkMyOUY0OCIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDo4Q0NGM0E3OTY1NkExMUUwQjdCNEE4Mzg3MkMyOUY0OCIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI%2FPqqezsUAAAAfSURBVHjaYmRABcYwBiM2QSA4y4hNEKYDQxAEAAIMAHNGAzhkPOlYAAAAAElFTkSuQmCC%29%20repeat%2Dx%200%200%3Bborder%3A0%20none%3Bcolor%3A%23ccc%3Bheight%3A4px%3Bpadding%3A0%7Dbody%3Eh2%3Afirst%2Dchild%7Bmargin%2Dtop%3A0%3Bpadding%2Dtop%3A0%7Dbody%3Eh1%3Afirst%2Dchild%7Bmargin%2Dtop%3A0%3Bpadding%2Dtop%3A0%7Dbody%3Eh1%3Afirst%2Dchild%2Bh2%7Bmargin%2Dtop%3A0%3Bpadding%2Dtop%3A0%7Dbody%3Eh3%3Afirst%2Dchild%2Cbody%3Eh4%3Afirst%2Dchild%2Cbody%3Eh5%3Afirst%2Dchild%2Cbody%3Eh6%3Afirst%2Dchild%7Bmargin%2Dtop%3A0%3Bpadding%2Dtop%3A0%7Da%3Afirst%2Dchild%20h1%2Ca%3Afirst%2Dchild%20h2%2Ca%3Afirst%2Dchild%20h3%2Ca%3Afirst%2Dchild%20h4%2Ca%3Afirst%2Dchild%20h5%2Ca%3Afirst%2Dchild%20h6%7Bmargin%2Dtop%3A0%3Bpadding%2Dtop%3A0%7Dh1%2Bp%2Ch2%2Bp%2Ch3%2Bp%2Ch4%2Bp%2Ch5%2Bp%2Ch6%2Bp%2Cul%20li%3E%3Afirst%2Dchild%2Col%20li%3E%3Afirst%2Dchild%7Bmargin%2Dtop%3A0%7Ddl%7Bpadding%3A0%7Ddl%20dt%7Bfont%2Dsize%3A14px%3Bfont%2Dweight%3Abold%3Bfont%2Dstyle%3Aitalic%3Bpadding%3A0%3Bmargin%3A15px%200%205px%7Ddl%20dt%3Afirst%2Dchild%7Bpadding%3A0%7Ddl%20dt%3E%3Afirst%2Dchild%7Bmargin%2Dtop%3A0%7Ddl%20dt%3E%3Alast%2Dchild%7Bmargin%2Dbottom%3A0%7Ddl%20dd%7Bmargin%3A0%200%2015px%3Bpadding%3A0%2015px%7Ddl%20dd%3E%3Afirst%2Dchild%7Bmargin%2Dtop%3A0%7Ddl%20dd%3E%3Alast%2Dchild%7Bmargin%2Dbottom%3A0%7Dblockquote%7Bborder%2Dleft%3A4px%20solid%20%23DDD%3Bpadding%3A0%2015px%3Bcolor%3A%23777%7Dblockquote%3E%3Afirst%2Dchild%7Bmargin%2Dtop%3A0%7Dblockquote%3E%3Alast%2Dchild%7Bmargin%2Dbottom%3A0%7Dtable%7Bborder%2Dcollapse%3Acollapse%3Bborder%2Dspacing%3A0%3Bfont%2Dsize%3A100%25%3Bfont%3Ainherit%7Dtable%20th%7Bfont%2Dweight%3Abold%3Bborder%3A1px%20solid%20%23ccc%3Bpadding%3A6px%2013px%7Dtable%20td%7Bborder%3A1px%20solid%20%23ccc%3Bpadding%3A6px%2013px%7Dtable%20tr%7Bborder%2Dtop%3A1px%20solid%20%23ccc%3Bbackground%2Dcolor%3A%23fff%7Dtable%20tr%3Anth%2Dchild%282n%29%7Bbackground%2Dcolor%3A%23f8f8f8%7Dimg%7Bmax%2Dwidth%3A100%25%7Dcode%2Ctt%7Bmargin%3A0%202px%3Bpadding%3A0%205px%3Bwhite%2Dspace%3Anowrap%3Bborder%3A1px%20solid%20%23eaeaea%3Bbackground%2Dcolor%3A%23f8f8f8%3Bborder%2Dradius%3A3px%3Bfont%2Dfamily%3AConsolas%2C%27Liberation%20Mono%27%2CCourier%2Cmonospace%3Bfont%2Dsize%3A12px%3Bcolor%3A%23333%7Dpre%3Ecode%7Bmargin%3A0%3Bpadding%3A0%3Bwhite%2Dspace%3Apre%3Bborder%3A0%3Bbackground%3Atransparent%7D%2Ehighlight%20pre%7Bbackground%2Dcolor%3A%23f8f8f8%3Bborder%3A1px%20solid%20%23ccc%3Bfont%2Dsize%3A13px%3Bline%2Dheight%3A19px%3Boverflow%3Aauto%3Bpadding%3A6px%2010px%3Bborder%2Dradius%3A3px%7Dpre%7Bbackground%2Dcolor%3A%23f8f8f8%3Bborder%3A1px%20solid%20%23ccc%3Bfont%2Dsize%3A13px%3Bline%2Dheight%3A19px%3Boverflow%3Aauto%3Bpadding%3A6px%2010px%3Bborder%2Dradius%3A3px%7Dpre%20code%2Cpre%20tt%7Bbackground%2Dcolor%3Atransparent%3Bborder%3A0%7D%2Epoetry%20pre%7Bfont%2Dfamily%3AGeorgia%2CGaramond%2Cserif%21important%3Bfont%2Dstyle%3Aitalic%3Bfont%2Dsize%3A110%25%21important%3Bline%2Dheight%3A1%2E6em%3Bdisplay%3Ablock%3Bmargin%2Dleft%3A1em%7D%2Epoetry%20pre%20code%7Bfont%2Dfamily%3AGeorgia%2CGaramond%2Cserif%21important%3Bword%2Dbreak%3Abreak%2Dall%3Bword%2Dbreak%3Abreak%2Dword%3B%2Dwebkit%2Dhyphens%3Aauto%3B%2Dmoz%2Dhyphens%3Aauto%3Bhyphens%3Aauto%3Bwhite%2Dspace%3Apre%2Dwrap%7Dsup%2Csub%2Ca%2Efootnote%7Bfont%2Dsize%3A1%2E4ex%3Bheight%3A0%3Bline%2Dheight%3A1%3Bvertical%2Dalign%3Asuper%3Bposition%3Arelative%7Dsub%7Bvertical%2Dalign%3Asub%3Btop%3A%2D1px%7D%40media%20print%7Bbody%7Bbackground%3A%23fff%7Dimg%2Cpre%2Cblockquote%2Ctable%2Cfigure%7Bpage%2Dbreak%2Dinside%3Aavoid%7Dbody%7Bbackground%3A%23fff%3Bborder%3A0%7Dcode%7Bbackground%2Dcolor%3A%23fff%3Bcolor%3A%23333%21important%3Bpadding%3A0%20%2E2em%3Bborder%3A1px%20solid%20%23dedede%7Dpre%7Bbackground%3A%23fff%7Dpre%20code%7Bbackground%2Dcolor%3Awhite%21important%3Boverflow%3Avisible%7D%7D%40media%20screen%7Bbody%2Einverted%7Bcolor%3A%23eee%21important%3Bborder%2Dcolor%3A%23555%3Bbox%2Dshadow%3Anone%7D%2Einverted%20body%2C%2Einverted%20hr%20%2Einverted%20p%2C%2Einverted%20td%2C%2Einverted%20li%2C%2Einverted%20h1%2C%2Einverted%20h2%2C%2Einverted%20h3%2C%2Einverted%20h4%2C%2Einverted%20h5%2C%2Einverted%20h6%2C%2Einverted%20th%2C%2Einverted%20%2Emath%2C%2Einverted%20caption%2C%2Einverted%20dd%2C%2Einverted%20dt%2C%2Einverted%20blockquote%7Bcolor%3A%23eee%21important%3Bborder%2Dcolor%3A%23555%3Bbox%2Dshadow%3Anone%7D%2Einverted%20td%2C%2Einverted%20th%7Bbackground%3A%23333%7D%2Einverted%20h2%7Bborder%2Dcolor%3A%23555%7D%2Einverted%20hr%7Bborder%2Dcolor%3A%23777%3Bborder%2Dwidth%3A1px%21important%7D%3A%3Aselection%7Bbackground%3Argba%28157%2C193%2C200%2C0%2E5%29%7Dh1%3A%3Aselection%7Bbackground%2Dcolor%3Argba%2845%2C156%2C208%2C0%2E3%29%7Dh2%3A%3Aselection%7Bbackground%2Dcolor%3Argba%2890%2C182%2C224%2C0%2E3%29%7Dh3%3A%3Aselection%2Ch4%3A%3Aselection%2Ch5%3A%3Aselection%2Ch6%3A%3Aselection%2Cli%3A%3Aselection%2Col%3A%3Aselection%7Bbackground%2Dcolor%3Argba%28133%2C201%2C232%2C0%2E3%29%7Dcode%3A%3Aselection%7Bbackground%2Dcolor%3Argba%280%2C0%2C0%2C0%2E7%29%3Bcolor%3A%23eee%7Dcode%20span%3A%3Aselection%7Bbackground%2Dcolor%3Argba%280%2C0%2C0%2C0%2E7%29%21important%3Bcolor%3A%23eee%21important%7Da%3A%3Aselection%7Bbackground%2Dcolor%3Argba%28255%2C230%2C102%2C0%2E2%29%7D%2Einverted%20a%3A%3Aselection%7Bbackground%2Dcolor%3Argba%28255%2C230%2C102%2C0%2E6%29%7Dtd%3A%3Aselection%2Cth%3A%3Aselection%2Ccaption%3A%3Aselection%7Bbackground%2Dcolor%3Argba%28180%2C237%2C95%2C0%2E5%29%7D%2Einverted%7Bbackground%3A%230b2531%3Bbackground%3A%23252a2a%7D%2Einverted%20body%7Bbackground%3A%23252a2a%7D%2Einverted%20a%7Bcolor%3A%23acd1d5%7D%7D%2Ehighlight%20%2Ec%7Bcolor%3A%23998%3Bfont%2Dstyle%3Aitalic%7D%2Ehighlight%20%2Eerr%7Bcolor%3A%23a61717%3Bbackground%2Dcolor%3A%23e3d2d2%7D%2Ehighlight%20%2Ek%2C%2Ehighlight%20%2Eo%7Bfont%2Dweight%3Abold%7D%2Ehighlight%20%2Ecm%7Bcolor%3A%23998%3Bfont%2Dstyle%3Aitalic%7D%2Ehighlight%20%2Ecp%7Bcolor%3A%23999%3Bfont%2Dweight%3Abold%7D%2Ehighlight%20%2Ec1%7Bcolor%3A%23998%3Bfont%2Dstyle%3Aitalic%7D%2Ehighlight%20%2Ecs%7Bcolor%3A%23999%3Bfont%2Dweight%3Abold%3Bfont%2Dstyle%3Aitalic%7D%2Ehighlight%20%2Egd%7Bcolor%3A%23000%3Bbackground%2Dcolor%3A%23fdd%7D%2Ehighlight%20%2Egd%20%2Ex%7Bcolor%3A%23000%3Bbackground%2Dcolor%3A%23faa%7D%2Ehighlight%20%2Ege%7Bfont%2Dstyle%3Aitalic%7D%2Ehighlight%20%2Egr%7Bcolor%3A%23a00%7D%2Ehighlight%20%2Egh%7Bcolor%3A%23999%7D%2Ehighlight%20%2Egi%7Bcolor%3A%23000%3Bbackground%2Dcolor%3A%23dfd%7D%2Ehighlight%20%2Egi%20%2Ex%7Bcolor%3A%23000%3Bbackground%2Dcolor%3A%23afa%7D%2Ehighlight%20%2Ego%7Bcolor%3A%23888%7D%2Ehighlight%20%2Egp%7Bcolor%3A%23555%7D%2Ehighlight%20%2Egs%7Bfont%2Dweight%3Abold%7D%2Ehighlight%20%2Egu%7Bcolor%3A%23800080%3Bfont%2Dweight%3Abold%7D%2Ehighlight%20%2Egt%7Bcolor%3A%23a00%7D%2Ehighlight%20%2Ekc%2C%2Ehighlight%20%2Ekd%2C%2Ehighlight%20%2Ekn%2C%2Ehighlight%20%2Ekp%2C%2Ehighlight%20%2Ekr%7Bfont%2Dweight%3Abold%7D%2Ehighlight%20%2Ekt%7Bcolor%3A%23458%3Bfont%2Dweight%3Abold%7D%2Ehighlight%20%2Em%7Bcolor%3A%23099%7D%2Ehighlight%20%2Es%7Bcolor%3A%23d14%7D%2Ehighlight%20%2Ena%7Bcolor%3A%23008080%7D%2Ehighlight%20%2Enb%7Bcolor%3A%230086b3%7D%2Ehighlight%20%2Enc%7Bcolor%3A%23458%3Bfont%2Dweight%3Abold%7D%2Ehighlight%20%2Eno%7Bcolor%3A%23008080%7D%2Ehighlight%20%2Eni%7Bcolor%3A%23800080%7D%2Ehighlight%20%2Ene%2C%2Ehighlight%20%2Enf%7Bcolor%3A%23900%3Bfont%2Dweight%3Abold%7D%2Ehighlight%20%2Enn%7Bcolor%3A%23555%7D%2Ehighlight%20%2Ent%7Bcolor%3A%23000080%7D%2Ehighlight%20%2Env%7Bcolor%3A%23008080%7D%2Ehighlight%20%2Eow%7Bfont%2Dweight%3Abold%7D%2Ehighlight%20%2Ew%7Bcolor%3A%23bbb%7D%2Ehighlight%20%2Emf%2C%2Ehighlight%20%2Emh%2C%2Ehighlight%20%2Emi%2C%2Ehighlight%20%2Emo%7Bcolor%3A%23099%7D%2Ehighlight%20%2Esb%2C%2Ehighlight%20%2Esc%2C%2Ehighlight%20%2Esd%2C%2Ehighlight%20%2Es2%2C%2Ehighlight%20%2Ese%2C%2Ehighlight%20%2Esh%2C%2Ehighlight%20%2Esi%2C%2Ehighlight%20%2Esx%7Bcolor%3A%23d14%7D%2Ehighlight%20%2Esr%7Bcolor%3A%23009926%7D%2Ehighlight%20%2Es1%7Bcolor%3A%23d14%7D%2Ehighlight%20%2Ess%7Bcolor%3A%23990073%7D%2Ehighlight%20%2Ebp%7Bcolor%3A%23999%7D%2Ehighlight%20%2Evc%2C%2Ehighlight%20%2Evg%2C%2Ehighlight%20%2Evi%7Bcolor%3A%23008080%7D%2Ehighlight%20%2Eil%7Bcolor%3A%23099%7D%2Ehighlight%20%2Egc%7Bcolor%3A%23999%3Bbackground%2Dcolor%3A%23eaf2f5%7D%2Etype%2Dcsharp%20%2Ehighlight%20%2Ek%2C%2Etype%2Dcsharp%20%2Ehighlight%20%2Ekt%7Bcolor%3A%2300F%7D%2Etype%2Dcsharp%20%2Ehighlight%20%2Enf%7Bcolor%3A%23000%3Bfont%2Dweight%3Anormal%7D%2Etype%2Dcsharp%20%2Ehighlight%20%2Enc%7Bcolor%3A%232b91af%7D%2Etype%2Dcsharp%20%2Ehighlight%20%2Enn%7Bcolor%3A%23000%7D%2Etype%2Dcsharp%20%2Ehighlight%20%2Es%2C%2Etype%2Dcsharp%20%2Ehighlight%20%2Esc%7Bcolor%3A%23a31515%7D" />
  <!--[if lt IE 9]>
    <script src="//cdnjs.cloudflare.com/ajax/libs/html5shiv/3.7.3/html5shiv-printshiv.min.js"></script>
  <![endif]-->
</head>
<body>
<h1 id="winmerge-21616-release-notes">WinMerge 2.16.16 Release Notes</h1>
<ul>
<li><a href="#about-this-release">About This Release</a></li>
<li><a href="#what-is-new-in-21616">What Is New in 2.16.16?</a></li>
<li><a href="#what-is-new-in-21615-beta">What Is New in 2.16.15 beta?</a></li>
<li><a href="#known-issues">Known issues</a></li>
</ul>
<p>October 2021</p>
<h2 id="about-this-release">About This Release</h2>
<p>This is a WinMerge 2.16.16 stable release. This release replaces earlier WinMerge stable releases as a recommended release.</p>
<p>Please submit bug reports to our <a href="http://github.com/winmerge/winmerge/issues">bug-tracker</a>.</p>
<h2 id="what-is-new-in-21616">What Is New in 2.16.16</h2>
<h3 id="general">General</h3>
<ul>
<li>Fix a problem where the string in the Windows common dialog would not change to the language when switching languages.</li>
</ul>
<h3 id="file-compare">File compare</h3>
<ul>
<li>BugFix: Fix not getting the proper error message when saving failed</li>
</ul>
<h3 id="table-compare">Table compare</h3>
<ul>
<li>BugFix: Cannot resize last column with UI (#998)</li>
<li>Reloading a file that was changed by another application does not preserve column widths (#951)</li>
</ul>
<h3 id="image-compare">Image compare</h3>
<ul>
<li>BugFix: Fix an issue where drag-and-drop of file would only work once.</li>
</ul>
<h3 id="folder-compare">Folder compare</h3>
<ul>
<li>BugFix: Sync (Super Slow) (#771)</li>
<li>BugFix: Fix an issue where filters are not applied correctly when opening a project file containing multiple items with different filters. (PR #995)</li>
<li>[Feature Request] New Display Columns: Dimensions + Size Difference (#131)</li>
<li>FolderCompare: Additional Properties (Windows Property System+Hash (MD5, SHA-1, SHA-256)) (PR #996)</li>
</ul>
<h3 id="options-dialog">Options dialog</h3>
<ul>
<li>BugFix: Fix the problem that the &quot;Register Shell Extension for Windows 11 or later&quot; button is not enabled when another user has registered ShellExtension for Windows 11.</li>
</ul>
<h3 id="plugins">Plugins</h3>
<ul>
<li>BugFix: Plugin unpacked file extension problem (get_PluginUnpackedFileExtension) (#983)</li>
<li>BugFix: Comparing broken lnk-files (windows shortcuts) freezes WinMerge (#1007)</li>
<li>Apache Tika plugin: Update Apache tika to 2.1.0 and change the download URL</li>
<li>CompareMSExcelFiles.sct: Make the number before the sheet name zero-padded</li>
</ul>
<h3 id="shell-extension">Shell extension</h3>
<ul>
<li>BugFix: ShellExtension for Windows 11 did not work on machines that did not have MSVCP140.dll VCRUNTIME140*.dll installed.</li>
<li>BugFix: Loop counter should be the same type as the count type. (PR #987)</li>
<li>ShellExtension for Windows11: Disable Registry Write Virtualization</li>
</ul>
<h3 id="manual">Manual</h3>
<ul>
<li>Where to report documentation/help errors? (#1004)</li>
</ul>
<h3 id="translations">Translations</h3>
<ul>
<li>Translation updates:
<ul>
<li>Galician (PR #1005)</li>
<li>German (PR #986,#1027)</li>
<li>Hungarian (PR #991,#1023)</li>
<li>Japanese</li>
<li>Lithuanian (PR #979)</li>
<li>Portuguese (PR #1017)</li>
<li>Slovenian (#1026)</li>
<li>Turkish (PR #980)</li>
</ul></li>
</ul>
<h3 id="internals">Internals</h3>
<ul>
<li>BugFix: Missing packages.config (also outdated) and wrong NuGet packages path in the WinMergeContextMenu.vcxproj and .filters file (#985)</li>
<li>Fix typo in OpenView.cpp (PR #1000)</li>
</ul>
<h2 id="what-is-new-in-21615-beta">What Is New in 2.16.15 Beta</h2>
<h3 id="general-1">General</h3>
<ul>
<li>BugFix: WinMerge would crash when launched if the registry or INI file contained invalid values.</li>
<li>BugFix: Winmerge Crash when comparing 2 files from Windows Explorer context menu (#808, #908, #913)</li>
<li>BugFix: Incorrect text color for selected menu item on Windows 11</li>
<li>BugFix: 50% cpu use by winmergeu.exe after program closed (#903)</li>
<li>Digitally sign packages (#152)</li>
</ul>
<h3 id="file-compare-1">File compare</h3>
<ul>
<li>BugFix: The mouse cursor did not change to an hourglass when the files or plugins were taking a long time to load.</li>
<li>BugFix: Save Middle and Save Middle As menu items were not enabled when comparing three files.</li>
<li>BugFix: A two-pane window was displayed even though New (3panes) → Table menu item was selected.</li>
<li>BugFix: The height of each pane in the Diff pane was calculated incorrectly when comparing three files.</li>
<li>BugFix: Unicode SMP chars misrendered after performing a find (#914)</li>
<li>BugFix: Crash when pressing Shift+F4 key</li>
<li>BugFix: Replace slow (#940)</li>
<li>BugFix: When moving in the scroll pane, the selected position is incorrect (#970)</li>
<li>BugFix: When the Diff pane was redisplayed, the scroll position of the Diff pane was not appropriate. (osdn.net #42862)</li>
<li>Make &quot;Do not close this box&quot; checkbox in search window On by default (#941)</li>
</ul>
<h3 id="image-compare-1">Image compare</h3>
<ul>
<li>BugFix: Duplicate menu shortcut in translations (#905)</li>
<li>BugFix: Image comparison (winimerge #24)</li>
</ul>
<h3 id="project-file">Project file</h3>
<ul>
<li>Add a feature to save/restore compare options to/from a project file.(#498) (PR #915)</li>
</ul>
<h3 id="options-dialog-1">Options dialog</h3>
<ul>
<li>Add a feature to set items saved to or restored from the project file. (PR #953)</li>
</ul>
<h3 id="plugins-1">Plugins</h3>
<ul>
<li>New unpacker plugins:
<ul>
<li>DecompileJVM</li>
<li>DecompileIL</li>
<li>DisassembleNative</li>
</ul></li>
</ul>
<h3 id="command-line">Command line</h3>
<ul>
<li>Added /c <code>column number</code> command line option</li>
<li>Added /EnableExitCode command line option</li>
</ul>
<h3 id="shell-extension-1">Shell extension</h3>
<ul>
<li>BugFix: WinMerge&#39;s extended menu items were doubly inserted into the context menu of Explorer&#39;s navigation pane. (osdn.net #42702)</li>
<li>BugFix: Right click - compare - is unclear (#249)</li>
<li>Added a new DLL (WinMergeContextMenu.dll) for the Windows 11 Explorer context menu (currently unstable and not registered by default) (PR #954)</li>
</ul>
<h3 id="translations-1">Translations</h3>
<ul>
<li>Translation updates:
<ul>
<li>Brazilian (PR #910)</li>
<li>Dutch (PR #921,#969)</li>
<li>German (PR #965,#977)</li>
<li>Hungarian (PR #937,#955)</li>
<li>Italian (PR #911)</li>
<li>Japanese</li>
<li>Korean (PR #932)</li>
<li>Portuguese (PR #956,#964,#976)</li>
<li>Russian (PR #901,#927,#963)</li>
<li>Slovenian</li>
<li>Swedish (PR #974)</li>
<li>Turkish (PR #899)</li>
</ul></li>
</ul>
<h3 id="internals-1">Internals</h3>
<ul>
<li>README.md: Make it clear that requirements are to build, not use the application (PR #942)</li>
<li>compiler-calculated maximum value for <code>m_SourceDefs</code> (PR #966)</li>
</ul>
<h2 id="known-issues">Known issues</h2>
<ul>
<li>Crashes when comparing large files (GitHub #325)</li>
<li>Very slow to compare significantly different directories (GitHub #322)</li>
<li>Vertical scrollbar breaks after pasting text (GitHub #296)</li>
</ul>
</body>
</html>
