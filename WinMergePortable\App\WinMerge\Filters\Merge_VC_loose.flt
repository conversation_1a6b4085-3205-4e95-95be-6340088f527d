## This is a directory/file filter for WinMerge
## This filter suppresses various binaries found in Visual C++ source trees
name: Visual C++ loose
desc: Suppresses various binaries found in Visual C++ source trees

## This is an inclusive (loose) filter
## (it lets through everything not specified)
def: include

## Filters for filenames begin with f:
## Filters for directories begin with d:
## (Inline comments begin with " ##" and extend to the end of the line)

f: \.aps$ ## VC Binary version of resource file, for quick loading
f: \.bsc$ ## VC Browser database
f: \.clw$ ## VC class-wizard status file
f: \.dll$ ## Windows DLL
f: \.exe$ ## Windows/DOS executable
f: \.exp$ ## VC library export file
f: ^BuildLog.htm$ ## VC build log file
f: ^vc\d+\.idb$ ## VC Minimal rebuild dependency file
f: \.ilk$ ## VC incremental linker memory file
f: \.lib$ ## compiled libraries
f: \.ncb$ ## VC parser information file (class view & component gallery stuff)
f: \.obj$ ## VC object module file
f: \.pch$ ## VC precompiled header file
f: \.pdb$ ## VC program database file (debugging symbolic information)
f: \.sbr$ ## VC source browser file (used to create bsc file)
f: \.res$ ## VC compiled resources file (output of RC [resource compiler])
f: \.suo$ ## VC options file (binary)
f: \.ncb$ ## VC Intellisense file
f: \.opt$ ## VC project options file
f: \.bak$ ## backup
f: \.sdf$ ## SQL Server Compact Edition Database File
f: \.VC.db$ ## VS Solution Database File
f: \.idb$ ## VC Rebuild Dependency Information

d: \\\.svn$ ## Subversion working copy
d: \\_svn$  ## Subversion working copy ASP.NET Hack
d: \\cvs$   ## CVS control directory
d: \\\.git$ ## Git directory
d: \\\.bzr$ ## Bazaar branch
d: \\\.hg$ ## Mercurial repository
d: \\\.vs$ ## A hidden folder that stores .suo and *.db files
d: \\ipch$ ## Intellisense cache for precompiled header file

