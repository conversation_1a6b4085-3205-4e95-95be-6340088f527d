# This file is part from WinMerge <https://winmerge.org/>
# Released under the "GNU General Public License"
#
# ID line follows -- this is updated by SVN
# $Id: $
#
# Translators:
# <PERSON>, 2021
#
msgid ""
msgstr ""
"Project-Id-Version: WinMerge Shell Extension\n"
"Report-Msgid-Bugs-To: https://bugs.winmerge.org/\n"
"POT-Creation-Date: 2021-08-17 20:34+0000\n"
"PO-Revision-Date: 2021-01-28 18:56+0000\n"
"Last-Translator: <PERSON>, 2021\n"
"Language-Team: Dutch (https://www.transifex.com/rockytdr/teams/91037/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Poedit-Basepath: ../../ShellExtension/Languages/\n"
"X-Poedit-Language: English\n"
"X-Poedit-SourceCharset: CP1252\n"

#. AFX_TARG_*
#: ShellExtension.rc:19
#, c-format
msgid "AFX_TARG_ENU"
msgstr "AFX_TARG_NLB"

#. LANGUAGE, SUBLANGUAGE
#: ShellExtension.rc:20
#, c-format
msgid "LANG_ENGLISH, SUBLANG_ENGLISH_US"
msgstr "LANG_DUTCH, SUBLANG_DUTCH"

#. Codepage
#: ShellExtension.rc:21
#, c-format
msgid "1252"
msgstr "1252"

#: ShellExtension.rc:111
#, c-format
msgid "ShellExtension"
msgstr "Contextmenu"

#: ShellExtension.rc:112
#, c-format
msgid "&WinMerge"
msgstr "&WinMerge"

#: ShellExtension.rc:113
#, c-format
msgid "&Compare"
msgstr "Vergelijken"

#: ShellExtension.rc:114
#, c-format
msgid "Compare&..."
msgstr "Vergelijken..."

#: ShellExtension.rc:115
#, c-format
msgid "Select &Left"
msgstr "Links selecteren"

#: ShellExtension.rc:116
#, c-format
msgid "Select &Middle"
msgstr "Midden selecteren"

#: ShellExtension.rc:117
#, c-format
msgid "Re-select &Left"
msgstr "Links opnieuw selecteren"
