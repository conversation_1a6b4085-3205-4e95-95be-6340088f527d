; Solarized Light color scheme for WinMerge
; based on https://ethanschoonover.com/solarized/ and https://github.com/keeleyt83/winmerge-solarized-dark
; license: MIT
[WinMerge]
; base03
Custom Colors/0=0x362b00
; base02
Custom Colors/1=0x423607
; base01
Custom Colors/2=0x756e58
; base00
Custom Colors/3=0x837b65
; base0
Custom Colors/4=0x969483
; base1
Custom Colors/5=0xa1a193
; base2
Custom Colors/6=0xd5e8ee
; base3
Custom Colors/7=0xe3f6fd
; yellow
Custom Colors/8=0x0089b5
; orange
Custom Colors/9=0x164bcb
; red
Custom Colors/10=0x2f32dc
; magenta
Custom Colors/11=0x8236d3
; violet
Custom Colors/12=0xc4716c
; blue
Custom Colors/13=0xc98b26
; cyan
Custom Colors/14=0x98a12a
; green
Custom Colors/15=0x009985

; Syntax Category
DefaultSyntaxColors/Bold00=0
DefaultSyntaxColors/Bold01=0
DefaultSyntaxColors/Bold02=0
DefaultSyntaxColors/Bold03=0
DefaultSyntaxColors/Bold04=0
DefaultSyntaxColors/Bold05=0
DefaultSyntaxColors/Bold06=0
DefaultSyntaxColors/Bold07=0
DefaultSyntaxColors/Bold08=0
DefaultSyntaxColors/Bold09=0
DefaultSyntaxColors/Bold10=0
DefaultSyntaxColors/Bold11=0
DefaultSyntaxColors/Bold12=0
DefaultSyntaxColors/Bold13=0
DefaultSyntaxColors/Bold14=0
DefaultSyntaxColors/Bold15=0
DefaultSyntaxColors/Bold16=0
DefaultSyntaxColors/Bold17=0
DefaultSyntaxColors/Bold18=0
DefaultSyntaxColors/Bold19=0
DefaultSyntaxColors/Bold20=0
DefaultSyntaxColors/Bold21=0
DefaultSyntaxColors/Bold22=0
DefaultSyntaxColors/Bold23=0
DefaultSyntaxColors/Bold24=0
DefaultSyntaxColors/Bold25=0
DefaultSyntaxColors/Color00=0x000080
; base3
DefaultSyntaxColors/Color01=0xe3f6fd
; base3
DefaultSyntaxColors/Color02=0xe3f6fd
; base01
DefaultSyntaxColors/Color03=0x756e58
; base2
DefaultSyntaxColors/Color04=0xd5e8ee
; base1
DefaultSyntaxColors/Color05=0xa1a193
; base3
DefaultSyntaxColors/Color06=0xe3f6fd
; yellow
DefaultSyntaxColors/Color07=0x0089b5
; base1
DefaultSyntaxColors/Color08=0xa1a193
DefaultSyntaxColors/Color09=0x0f42ca
DefaultSyntaxColors/Color10=0x0f42ca
; base1
DefaultSyntaxColors/Color11=0xa1a193
; cyan
DefaultSyntaxColors/Color12=0x98a12a
; blue
DefaultSyntaxColors/Color13=0xc98b26
DefaultSyntaxColors/Color14=0xa0a0ff
DefaultSyntaxColors/Color15=0x000000
DefaultSyntaxColors/Color16=0x00ffff
DefaultSyntaxColors/Color17=0x000000
DefaultSyntaxColors/Color18=0x000080
DefaultSyntaxColors/Color19=0x000080
; violet
DefaultSyntaxColors/Color20=0xc4716c
; base2
DefaultSyntaxColors/Color21=0xd5e8ee
; yellow
DefaultSyntaxColors/Color22=0x0089b5
; cyan
DefaultSyntaxColors/Color23=0x98a12a
; magenta
DefaultSyntaxColors/Color24=0x8236d3
; violet
DefaultSyntaxColors/Color25=0xc4716c
DefaultSyntaxColors/Values=26
Settings/DefaultTextColoring=0

; Folder Compare Category
; base2
Settings/DirItemDiffColor=0xd5e8ee
; orange
Settings/DirItemDiffTextColor=0x164bcb
; base3
Settings/DirItemEqualColor=0xe3f6fd
; base00
Settings/DirItemEqualTextColor=0x837b65
; base2
Settings/DirItemFilteredColor=0xd5e8ee
; base02
Settings/DirItemFilteredTextColor=0x423607
; base1
Settings/DirItemNotExistAllColor=0xa1a193
; base3
Settings/DirItemNotExistAllTextColor=0xe3f6fd
; base2
Settings/DirMarginColor=0xd5e8ee

; Differences Category
Settings/DifferenceColor=0xddffdd
Settings/DifferenceDeletedColor=0xffe0e0
Settings/DifferenceTextColor=0xffffffff
Settings/MovedBlockColor=0xc0d8f0
Settings/MovedBlockDeletedColor=0xc0c0c0
Settings/MovedBlockTextColor=0xffffffff
Settings/SNPColor=0xdffafb
Settings/SNPDeletedColor=0xe9e9e9
Settings/SNPTextColor=0xffffffff
Settings/SelectedDifferenceColor=0xddddff
Settings/SelectedDifferenceDeletedColor=0xc0c0f0
Settings/SelectedDifferenceTextColor=0xffffffff
Settings/SelectedMovedBlockColor=0x4e70f8
Settings/SelectedMovedBlockDeletedColor=0xa3b5fc
Settings/SelectedMovedBlockTextColor=0xffffffff
Settings/SelectedSNPColor=0xb4b7ef
Settings/SelectedSNPDeletedColor=0xe0e0f0
Settings/SelectedSNPTextColor=0xffffffff
Settings/SelectedWordDifferenceColor=0xaaaaff
Settings/SelectedWordDifferenceDeletedColor=0x8c96e6
Settings/SelectedWordDifferenceTextColor=0xffffffff
Settings/TrivialDifferenceColor=0xbff2fb
Settings/TrivialDifferenceDeletedColor=0xe9e9e9
Settings/TrivialDifferenceTextColor=0xffffffff
Settings/UseDirCompareColors=1
Settings/WordDifferenceColor=0xaaffaa
Settings/WordDifferenceDeletedColor=0xa0e6a0
Settings/WordDifferenceTextColor=0xffffffff
