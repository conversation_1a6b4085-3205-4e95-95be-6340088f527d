import tkinter as tk
from tkinter import ttk, filedialog, messagebox, font
import fitz  # PyMuPDF
from PIL import Image, ImageTk
import io

class ResizableTextBox:
    def __init__(self, canvas, x, y, width=200, height=100, proofreading_mode=None, move_distance_func=None, add_undo_record=None):
        self.canvas = canvas
        self.x = x
        self.y = y
        self.width = width
        self.height = height
        self.min_size = 20
        self.border_width = 1  # 設定為最細的線
        self.selected = False
        self.dragging = False
        self.resizing = False
        self.resize_direction = None
        self.drag_start_x = 0
        self.drag_start_y = 0
        self.proofreading_mode = proofreading_mode  # 校對模式檢查函數
        self.move_distance_func = move_distance_func  # 獲取移動距離的函數
        self.add_undo_record = add_undo_record  # 添加復原記錄的函數
        
        # 字體設定
        self.current_font_family = "標楷體"
        self.current_font_size = 12
        
        # 創建文字框架 - 直接設定紅色邊框
        self.frame = tk.Frame(canvas, bg='white', relief='solid', bd=self.border_width)
        # 設定邊框顏色為紅色
        self.frame.configure(highlightbackground='red', highlightthickness=self.border_width, highlightcolor='red', bd=0)
        
        self.text_widget = tk.Text(self.frame, wrap=tk.WORD, font=('Arial', 12), bd=0)
        
        # 添加滾動條
        scrollbar = ttk.Scrollbar(self.frame, orient='vertical', command=self.text_widget.yview)
        self.text_widget.configure(yscrollcommand=scrollbar.set)
        
        self.text_widget.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')
        
        # 在畫布上放置框架
        self.window_id = canvas.create_window(x, y, window=self.frame, anchor='nw', 
                                            width=width, height=height)
        
        # 綁定事件
        self.frame.bind('<Button-1>', self.on_click)
        self.frame.bind('<B1-Motion>', self.on_drag)
        self.frame.bind('<ButtonRelease-1>', self.on_release)
        self.frame.bind('<Motion>', self.on_motion)
        
        # 移動功能 - 只使用Ctrl+左鍵
        for widget in [self.frame, self.text_widget]:
            widget.bind('<Control-Button-1>', self.on_move_click)  # Ctrl+左鍵移動
            widget.bind('<Control-B1-Motion>', self.on_move_drag)
            widget.bind('<Control-ButtonRelease-1>', self.on_move_release)
        
        # 校對模式功能 - 點擊文字框時的處理
        self.text_widget.bind('<Button-1>', self.on_text_click)
        
    def on_click(self, event):
        self.selected = True
        self.drag_start_x = event.x_root
        self.drag_start_y = event.y_root
        
        # 檢查是否在邊界上（調整大小）
        edge_width = 10
        if (event.x <= edge_width or event.x >= self.width - edge_width or 
            event.y <= edge_width or event.y >= self.height - edge_width):
            self.resizing = True
            self.resize_direction = self.get_resize_direction(event.x, event.y)
        else:
            self.dragging = True
        
        # 阻止事件傳播到文字框
        return "break"
            
    def on_move_click(self, event):
        """Ctrl+左鍵移動模式"""
        self.selected = True
        self.drag_start_x = event.x_root
        self.drag_start_y = event.y_root
        self.dragging = True
        return "break"  # 阻止事件傳播
        
    def on_move_drag(self, event):
        """移動拖拽"""
        if self.dragging:
            dx = event.x_root - self.drag_start_x
            dy = event.y_root - self.drag_start_y
            self.x += dx
            self.y += dy
            self.canvas.coords(self.window_id, self.x, self.y)
            self.drag_start_x = event.x_root
            self.drag_start_y = event.y_root
        return "break"
            
    def on_move_release(self, event):
        """移動拖拽釋放"""
        self.dragging = False
        return "break"
    
    def on_text_click(self, event):
        """處理文字框點擊事件（校對模式）"""
        if self.proofreading_mode and self.proofreading_mode():
            # 校對模式開啟時，刪除游標左邊的所有文字（包含點到的字符）
            try:
                # 獲取點擊位置的游標索引
                click_index = self.text_widget.index(f"@{event.x},{event.y}")
                
                # 將索引向右移動一個字符，這樣會包含點到的字符
                click_index_plus_one = self.text_widget.index(f"{click_index}+1c")
                
                # 記錄操作前的狀態用於復原
                if self.add_undo_record:
                    original_text = self.text_widget.get("1.0", "end-1c")
                    original_x = self.x
                    original_y = self.y
                    deleted_text = self.text_widget.get("1.0", click_index_plus_one)
                    remaining_text = self.text_widget.get(click_index_plus_one, "end-1c")
                    
                    # 創建復原記錄
                    undo_record = {
                        'textbox': self,
                        'action': 'proofreading_delete',
                        'original_text': original_text,
                        'original_x': original_x,
                        'original_y': original_y,
                        'deleted_text': deleted_text,
                        'remaining_text': remaining_text,
                        'click_index': click_index_plus_one
                    }
                    self.add_undo_record(undo_record)
                
                # 刪除從開始到點擊位置+1個字符的所有文字
                self.text_widget.delete("1.0", click_index_plus_one)
                
                # 將游標設定到開始位置
                self.text_widget.mark_set("insert", "1.0")
                
                # 向下移動文字框
                self.move_textbox_down()
                
                return "break"  # 阻止預設的點擊行為
            except Exception as e:
                print(f"校對模式錯誤: {e}")
        
        # 如果不是校對模式，允許正常點擊行為
        return None
    
    def move_textbox_down(self):
        """向下移動文字框"""
        try:
            # 從主應用程式獲取移動距離
            move_distance = self.get_move_distance()
            
            # 更新文字框位置
            self.y += move_distance
            self.canvas.coords(self.window_id, self.x, self.y)
            
        except Exception as e:
            print(f"移動文字框錯誤: {e}")
    
    def get_move_distance(self):
        """獲取移動距離"""
        if self.move_distance_func:
            return self.move_distance_func()
        return 20  # 預設值
        
    def restore_state(self, text, x, y):
        """恢復文字框的狀態"""
        try:
            # 恢復文字內容
            self.text_widget.delete("1.0", "end")
            self.text_widget.insert("1.0", text)
            
            # 恢復位置
            self.x = x
            self.y = y
            self.canvas.coords(self.window_id, self.x, self.y)
            
            # 將游標設定到開始位置
            self.text_widget.mark_set("insert", "1.0")
            
        except Exception as e:
            print(f"恢復狀態錯誤: {e}")

        
    def on_drag(self, event):
        if self.dragging:
            # 移動文字框
            dx = event.x_root - self.drag_start_x
            dy = event.y_root - self.drag_start_y
            self.x += dx
            self.y += dy
            self.canvas.coords(self.window_id, self.x, self.y)
            self.drag_start_x = event.x_root
            self.drag_start_y = event.y_root
            
        elif self.resizing:
            # 調整大小
            dx = event.x_root - self.drag_start_x
            dy = event.y_root - self.drag_start_y
            
            new_width = self.width
            new_height = self.height
            new_x = self.x
            new_y = self.y
            
            if 'left' in self.resize_direction:
                new_width = max(self.min_size, self.width - dx)
                new_x = self.x + (self.width - new_width)
            elif 'right' in self.resize_direction:
                new_width = max(self.min_size, self.width + dx)
                
            if 'top' in self.resize_direction:
                new_height = max(self.min_size, self.height - dy)
                new_y = self.y + (self.height - new_height)
            elif 'bottom' in self.resize_direction:
                new_height = max(self.min_size, self.height + dy)
            
            self.width = new_width
            self.height = new_height
            self.x = new_x
            self.y = new_y
            
            self.canvas.coords(self.window_id, self.x, self.y)
            self.canvas.itemconfig(self.window_id, width=self.width, height=self.height)
            
            self.drag_start_x = event.x_root
            self.drag_start_y = event.y_root
            
    def on_release(self, event):
        self.dragging = False
        self.resizing = False
        self.resize_direction = None
        
    def on_motion(self, event):
        if not self.resizing and not self.dragging:
            cursor = self.get_cursor_for_position(event.x, event.y)
            self.frame.config(cursor=cursor)
            
    def get_resize_direction(self, x, y):
        edge_width = 10
        direction = []
        
        if y <= edge_width:
            direction.append('top')
        elif y >= self.height - edge_width:
            direction.append('bottom')
            
        if x <= edge_width:
            direction.append('left')
        elif x >= self.width - edge_width:
            direction.append('right')
            
        return '_'.join(direction) if direction else None
        
    def get_cursor_for_position(self, x, y):
        edge_width = 10
        
        if ((x <= edge_width or x >= self.width - edge_width) and 
            (y <= edge_width or y >= self.height - edge_width)):
            if (x <= edge_width and y <= edge_width) or (x >= self.width - edge_width and y >= self.height - edge_width):
                return 'size_nw_se'
            else:
                return 'size_ne_sw'
        elif x <= edge_width or x >= self.width - edge_width:
            return 'size_we'
        elif y <= edge_width or y >= self.height - edge_width:
            return 'size_ns'
        else:
            return 'fleur'
            
    def set_font(self, font_family, font_size):
        """設定字體"""
        try:
            # 確保字體大小是整數或浮點數
            if isinstance(font_size, str):
                font_size = float(font_size)
            
            # 建立字體物件，使用固定的標楷體
            new_font = font.Font(family="標楷體", size=int(font_size))
            self.text_widget.config(font=new_font)
            
            # 記錄當前字體設定
            self.current_font_family = "標楷體"
            self.current_font_size = font_size
            
        except Exception as e:
            print(f"字體設定錯誤: {e}")
            # 使用預設字體
            try:
                default_font = font.Font(family="標楷體", size=12)
                self.text_widget.config(font=default_font)
            except:
                pass

class PDFReader:
    def __init__(self, root):
        self.root = root
        self.root.title("PDF閱讀器")
        self.root.geometry("1200x800")
        
        self.pdf_document = None
        self.current_page = 0
        self.zoom_level = 1.0
        self.text_boxes = []
        self.proofreading_mode = False  # 校對模式開關
        
        # 復原功能相關
        self.undo_history = []  # 復原歷史記錄
        self.max_undo_history = 50  # 最大復原記錄數
        
        self.setup_ui()
        
    def setup_ui(self):
        # 主菜單
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="檔案", menu=file_menu)
        file_menu.add_command(label="開啟PDF", command=self.open_pdf)
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=self.root.quit)
        
        # 工具欄
        toolbar = ttk.Frame(self.root)
        toolbar.pack(side=tk.TOP, fill=tk.X, padx=5, pady=5)
        
        # PDF控制
        ttk.Button(toolbar, text="開啟PDF", command=self.open_pdf).pack(side=tk.LEFT, padx=2)
        
        # 頁面控制
        ttk.Label(toolbar, text="頁面:").pack(side=tk.LEFT, padx=5)
        self.page_var = tk.StringVar(value="0")
        self.page_entry = ttk.Entry(toolbar, textvariable=self.page_var, width=5)
        self.page_entry.pack(side=tk.LEFT, padx=2)
        self.page_entry.bind('<Return>', self.goto_page)
        
        self.page_label = ttk.Label(toolbar, text="/ 0")
        self.page_label.pack(side=tk.LEFT, padx=2)
        
        ttk.Button(toolbar, text="上一頁", command=self.prev_page).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar, text="下一頁", command=self.next_page).pack(side=tk.LEFT, padx=2)
        
        # 縮放控制
        ttk.Label(toolbar, text="縮放:").pack(side=tk.LEFT, padx=5)
        self.zoom_var = tk.StringVar(value="100")
        zoom_entry = ttk.Entry(toolbar, textvariable=self.zoom_var, width=6)
        zoom_entry.pack(side=tk.LEFT, padx=2)
        zoom_entry.bind('<Return>', self.set_zoom)
        
        ttk.Label(toolbar, text="%").pack(side=tk.LEFT)
        ttk.Button(toolbar, text="設定", command=self.set_zoom).pack(side=tk.LEFT, padx=2)
        
        # 文字框控制
        ttk.Separator(toolbar, orient='vertical').pack(side=tk.LEFT, fill=tk.Y, padx=10)
        ttk.Button(toolbar, text="新增文字框", command=self.add_text_box).pack(side=tk.LEFT, padx=2)
        
        # 校對模式按鈕
        self.proofreading_button = ttk.Button(toolbar, text="校對模式: 關", command=self.toggle_proofreading_mode)
        self.proofreading_button.pack(side=tk.LEFT, padx=2)
        
        # 復原按鈕
        self.undo_button = ttk.Button(toolbar, text="復原 (Ctrl+Z)", command=self.undo_last_action, state='disabled')
        self.undo_button.pack(side=tk.LEFT, padx=2)
        
        # 校對模式移動距離控制
        ttk.Label(toolbar, text="移動距離:").pack(side=tk.LEFT, padx=5)
        self.move_distance_var = tk.StringVar(value="20")
        move_distance_entry = ttk.Entry(toolbar, textvariable=self.move_distance_var, width=6)
        move_distance_entry.pack(side=tk.LEFT, padx=2)
        ttk.Label(toolbar, text="px").pack(side=tk.LEFT)
        
        # 字體大小控制
        ttk.Label(toolbar, text="字體大小:").pack(side=tk.LEFT, padx=5)
        self.font_size_var = tk.StringVar(value="12")
        font_size_entry = ttk.Entry(toolbar, textvariable=self.font_size_var, width=6)
        font_size_entry.pack(side=tk.LEFT, padx=2)
        font_size_entry.bind('<Return>', self.update_font)
        
        ttk.Button(toolbar, text="設定字體", command=self.update_font).pack(side=tk.LEFT, padx=2)
        
        # 主要顯示區域
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # PDF顯示畫布
        self.canvas = tk.Canvas(main_frame, bg='white')
        
        # 滾動條
        v_scrollbar = ttk.Scrollbar(main_frame, orient='vertical', command=self.canvas.yview)
        h_scrollbar = ttk.Scrollbar(main_frame, orient='horizontal', command=self.canvas.xview)
        
        self.canvas.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # 佈局
        self.canvas.grid(row=0, column=0, sticky='nsew')
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')
        
        main_frame.grid_rowconfigure(0, weight=1)
        main_frame.grid_columnconfigure(0, weight=1)
        
        # 滑鼠滾輪支援
        self.canvas.bind('<MouseWheel>', self.on_mousewheel)
        self.canvas.bind('<Button-4>', self.on_mousewheel)
        self.canvas.bind('<Button-5>', self.on_mousewheel)
        
        # 雙擊添加文字框
        self.canvas.bind('<Double-Button-1>', self.add_text_box_at_position)
        
        # 鍵盤快捷鍵
        self.root.bind('<Control-z>', lambda e: self.undo_last_action())
        self.root.bind('<Control-Z>', lambda e: self.undo_last_action())
        
        # 狀態欄
        self.status_bar = ttk.Label(self.root, text="請開啟PDF檔案 | 提示：Ctrl+拖拽移動文字框，拖拽邊緣調整大小，Ctrl+Z復原")
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
        
    def add_undo_record(self, record):
        """添加復原記錄"""
        self.undo_history.append(record)
        
        # 限制復原記錄數量
        if len(self.undo_history) > self.max_undo_history:
            self.undo_history.pop(0)
        
        # 啟用復原按鈕
        self.undo_button.config(state='normal')
        
        # 更新狀態欄顯示復原記錄數量
        record_count = len(self.undo_history)
        action_name = record.get('action', '未知操作')
        self.status_bar.config(text=f"已記錄操作: {action_name} (可復原操作: {record_count})")
    
    def undo_last_action(self):
        """復原上一個操作"""
        if not self.undo_history:
            messagebox.showinfo("訊息", "沒有可復原的操作")
            return
        
        try:
            # 取得最後一個操作記錄
            last_record = self.undo_history.pop()
            
            if last_record['action'] == 'proofreading_delete':
                # 復原校對模式的刪除操作
                textbox = last_record['textbox']
                original_text = last_record['original_text']
                original_x = last_record['original_x']
                original_y = last_record['original_y']
                
                # 恢復文字框狀態
                textbox.restore_state(original_text, original_x, original_y)
                
                self.status_bar.config(text="已復原校對模式刪除操作")
            
            # 如果沒有更多復原記錄，禁用復原按鈕
            if not self.undo_history:
                self.undo_button.config(state='disabled')
                self.status_bar.config(text="沒有更多可復原的操作")
            else:
                record_count = len(self.undo_history)
                self.status_bar.config(text=f"復原成功 (剩餘可復原操作: {record_count})")
                
        except Exception as e:
            messagebox.showerror("錯誤", f"復原操作失敗: {str(e)}")
            # 如果復原失敗，將記錄重新加回去
            if 'last_record' in locals():
                self.undo_history.append(last_record)
        
    def open_pdf(self):
        file_path = filedialog.askopenfilename(
            title="選擇PDF檔案",
            filetypes=[("PDF files", "*.pdf"), ("All files", "*.*")]
        )
        
        if file_path:
            try:
                self.pdf_document = fitz.open(file_path)
                self.current_page = 0
                self.page_var.set("1")
                self.page_label.config(text=f"/ {len(self.pdf_document)}")
                self.display_page()
                
                # 清空復原歷史
                self.undo_history.clear()
                self.undo_button.config(state='disabled')
                
                self.status_bar.config(text=f"已開啟: {file_path}")
            except Exception as e:
                messagebox.showerror("錯誤", f"無法開啟PDF檔案: {str(e)}")
                
    def display_page(self):
        if not self.pdf_document:
            return
            
        try:
            page = self.pdf_document[self.current_page]
            mat = fitz.Matrix(self.zoom_level, self.zoom_level)
            pix = page.get_pixmap(matrix=mat)
            
            # 轉換為PIL Image
            img_data = pix.tobytes("ppm")
            image = Image.open(io.BytesIO(img_data))
            
            # 轉換為Tkinter PhotoImage
            self.photo = ImageTk.PhotoImage(image)
            
            # 清除畫布並顯示新頁面
            self.canvas.delete("pdf_page")
            self.canvas.create_image(0, 0, anchor=tk.NW, image=self.photo, tags="pdf_page")
            
            # 更新滾動區域
            self.canvas.configure(scrollregion=self.canvas.bbox("all"))
            
        except Exception as e:
            messagebox.showerror("錯誤", f"無法顯示頁面: {str(e)}")
            
    def prev_page(self):
        if self.pdf_document and self.current_page > 0:
            self.current_page -= 1
            self.page_var.set(str(self.current_page + 1))
            self.display_page()
            
    def next_page(self):
        if self.pdf_document and self.current_page < len(self.pdf_document) - 1:
            self.current_page += 1
            self.page_var.set(str(self.current_page + 1))
            self.display_page()
            
    def goto_page(self, event=None):
        if not self.pdf_document:
            return
            
        try:
            page_num = int(self.page_var.get()) - 1
            if 0 <= page_num < len(self.pdf_document):
                self.current_page = page_num
                self.display_page()
            else:
                messagebox.showwarning("警告", "頁面號碼超出範圍")
                self.page_var.set(str(self.current_page + 1))
        except ValueError:
            messagebox.showerror("錯誤", "請輸入有效的頁面號碼")
            self.page_var.set(str(self.current_page + 1))
            
    def set_zoom(self, event=None):
        try:
            zoom_percent = float(self.zoom_var.get())
            if zoom_percent > 0:
                self.zoom_level = zoom_percent / 100.0
                self.display_page()
            else:
                messagebox.showwarning("警告", "縮放比例必須大於0")
        except ValueError:
            messagebox.showerror("錯誤", "請輸入有效的縮放比例")
            
    def on_mousewheel(self, event):
        if event.num == 4 or event.delta > 0:
            self.canvas.yview_scroll(-1, "units")
        elif event.num == 5 or event.delta < 0:
            self.canvas.yview_scroll(1, "units")
            
    def add_text_box(self):
        x = 50
        y = 50
        text_box = ResizableTextBox(self.canvas, x, y, 
                                   proofreading_mode=lambda: self.proofreading_mode,
                                   move_distance_func=self.get_move_distance,
                                   add_undo_record=self.add_undo_record)
        self.text_boxes.append(text_box)
        
    def add_text_box_at_position(self, event):
        x = self.canvas.canvasx(event.x)
        y = self.canvas.canvasy(event.y)
        text_box = ResizableTextBox(self.canvas, x, y, 
                                   proofreading_mode=lambda: self.proofreading_mode,
                                   move_distance_func=self.get_move_distance,
                                   add_undo_record=self.add_undo_record)
        self.text_boxes.append(text_box)
    
    def toggle_proofreading_mode(self):
        """切換校對模式"""
        self.proofreading_mode = not self.proofreading_mode
        if self.proofreading_mode:
            self.proofreading_button.config(text="校對模式: 開")
            self.status_bar.config(text="校對模式已開啟 - 點擊文字框中的位置會刪除文字並向下移動文字框 (Ctrl+Z可復原)")
        else:
            self.proofreading_button.config(text="校對模式: 關")
            if self.undo_history:
                record_count = len(self.undo_history)
                self.status_bar.config(text=f"校對模式已關閉 (有 {record_count} 個可復原操作)")
            else:
                self.status_bar.config(text="校對模式已關閉")
    
    def get_move_distance(self):
        """獲取校對模式的移動距離"""
        try:
            distance = float(self.move_distance_var.get())
            return max(0, distance)  # 確保距離不為負數
        except ValueError:
            return 20  # 如果輸入無效，使用預設值
        
    def update_font(self, event=None):
        try:
            font_size_str = self.font_size_var.get()
            
            # 檢查字體大小是否為有效數字
            try:
                font_size = float(font_size_str)
                if font_size <= 0:
                    raise ValueError("字體大小必須大於0")
            except ValueError as e:
                messagebox.showerror("錯誤", f"請輸入有效的字體大小: {str(e)}")
                return
            
            # 更新所有文字框的字體
            updated_count = 0
            for text_box in self.text_boxes:
                text_box.set_font("標楷體", font_size)
                updated_count += 1
                
            if updated_count > 0:
                self.status_bar.config(text=f"已更新 {updated_count} 個文字框的字體大小為 {font_size}")
            else:
                self.status_bar.config(text="沒有文字框需要更新字體")
                
        except Exception as e:
            messagebox.showerror("錯誤", f"字體更新失敗: {str(e)}")

def main():
    root = tk.Tk()
    app = PDFReader(root)
    root.mainloop()

if __name__ == "__main__":
    main()