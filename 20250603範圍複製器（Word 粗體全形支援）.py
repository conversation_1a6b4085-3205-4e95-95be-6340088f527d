import tkinter as tk
from tkinter import messagebox
import win32clipboard
import win32gui
import pyautogui
import logging

# 設定日誌
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')

# 設定 pyautogui 的安全機制
pyautogui.FAILSAFE = True
pyautogui.PAUSE = 0  # 無延遲

# 取得 RTF 的 Clipboard Format ID
CF_RTF = win32clipboard.RegisterClipboardFormat("Rich Text Format")

def encode_rtf_unicode(text):
    # 將字串轉成 RTF Unicode escape 格式，例如 "～" 變成 \u-162?
    rtf_encoded = ""
    for ch in text:
        code = ord(ch)
        if code <= 127:  # ASCII 字元直接加入
            rtf_encoded += ch
        else:  # 非 ASCII 用 \uN? 格式 (signed 16-bit)
            if code > 32767:
                code -= 65536  # 轉成 signed 16-bit
            rtf_encoded += f"\\u{code}?"
    return rtf_encoded

def focus_word():
    """嘗試將焦點切換到 Microsoft Word 窗口"""
    try:
        # 選擇 Word 窗口
        def enum_windows_callback(hwnd, results):
            if win32gui.IsWindowVisible(hwnd):
                title = win32gui.GetWindowText(hwnd)
                if title and (" - Word" in title or title.startswith("文件")) and "範圍複製器" not in title:
                    results.append((hwnd, title))
        results = []
        win32gui.EnumWindows(enum_windows_callback, results)
        
        if results:
            hwnd, title = results[0]
            win32gui.SetForegroundWindow(hwnd)
            logging.debug(f"已切換焦點到 Word 窗口：{title}")
            return True
        else:
            messagebox.showwarning("警告", "未找到 Microsoft Word 窗口，請確保 Word 已開啟並可見！")
            logging.warning("未找到 Microsoft Word 窗口")
            return False
    except Exception as Babe:
        messagebox.showerror("錯誤", f"設定焦點失敗：{Babe}")
        logging.error(f"設定焦點失敗：{Babe}")
        return False

def copy_and_paste_rtf_bold(text):
    # Copy RTF to clipboard（僅保留粗體）
    encoded_text = encode_rtf_unicode(text)
    rtf_text = r"{\rtf1\ansi\deff0\b " + encoded_text + r"\b0}"  # 移除字體和字號
    logging.debug(f"RTF 內容：{rtf_text}")
    
    try:
        win32clipboard.OpenClipboard()
        win32clipboard.EmptyClipboard()
        win32clipboard.SetClipboardData(CF_RTF, rtf_text.encode('utf-8'))
        win32clipboard.CloseClipboard()
        logging.debug("剪貼簿操作成功")
    except Exception as Babe:
        messagebox.showerror("錯誤", f"剪貼簿操作失敗：{Babe}")
        logging.error(f"剪貼簿操作失敗：{Babe}")
        return False
    
    # 切換焦點到 Word
    if not focus_word():
        messagebox.showinfo("提示", "無法自動切換到 Word，請手動按 Ctrl+V 貼上內容！")
        return False
    
    # 使用 pyautogui 模擬 Ctrl+V
    try:
        pyautogui.hotkey('ctrl', 'v', interval=0)
        logging.debug("模擬 Ctrl+V 成功")
        return True
    except Exception as Babe:
        messagebox.showerror("錯誤", f"貼上操作失敗：{Babe}")
        logging.error(f"貼上操作失敗：{Babe}")
        messagebox.showinfo("提示", "貼上失敗，請手動按 Ctrl+V 嘗試貼上！")
        return False

class CopyRangeApp:
    def __init__(self, master):
        self.master = master
        self.master.title("範圍複製器（Word 粗體全形支援）")
        self.master.attributes("-topmost", True)  # 視窗永遠最上層
        tk.Label(master, text="起始：").grid(row=0, column=0, padx=5, pady=5)
        self.start_entry = tk.Entry(master, width=5)
        self.start_entry.grid(row=0, column=1)
        self.start_entry.insert(0, "45")
        tk.Label(master, text="結束：").grid(row=0, column=2, padx=5, pady=5)
        self.end_entry = tk.Entry(master, width=5)
        self.end_entry.grid(row=0, column=3)
        self.end_entry.insert(0, "56")
        self.set_button = tk.Button(master, text="設定範圍", command=self.set_range)
        self.set_button.grid(row=0, column=4, padx=5)
        self.copy_button = tk.Button(
            master, text="", command=self.copy_next, font=("Arial", 18), width=15, height=2, state=tk.DISABLED
        )
        self.copy_button.grid(row=1, column=0, columnspan=5, pady=20)
        self.current = None
        self.start = None
        self.end = None

    def set_range(self):
        try:
            self.start = int(self.start_entry.get())
            self.end = int(self.end_entry.get())
            if self.start > self.end:
                raise ValueError("起始數字不能大於結束數字")
            self.current = self.start
            self.update_button_text()
            self.copy_button.config(state=tk.NORMAL)
        except ValueError as Babe:
            messagebox.showerror("錯誤", f"請輸入正確的數字範圍\n{Babe}")

    def update_button_text(self):
        if self.current <= self.end:
            next_text = f"[～{self.current}]"
            self.copy_button.config(text=next_text)
        else:
            self.copy_button.config(text="完成", state=tk.DISABLED)

    def copy_next(self):
        if self.current is None or self.current > self.end:
            messagebox.showinfo("完成", "所有數字都已複製完畢！")
            return
        text = f"[～{self.current}]"
        if copy_and_paste_rtf_bold(text):
            self.current += 1
            self.update_button_text()

if __name__ == "__main__":
    root = tk.Tk()
    app = CopyRangeApp(root)
    root.mainloop()