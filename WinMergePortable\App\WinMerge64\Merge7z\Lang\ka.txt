﻿;!@Lang2@!UTF-8!
;  9.23 : 2011-09-25 : Translated by <PERSON><PERSON><PERSON>, original translation by <PERSON>, 
;
;
;
;
;
;
;
;
;
;
0
7-Zip
Georgian
ქართული
401
OK
გაუქმება



&დიახ
&არა
&დახურვა
დახმარება

&გაგრძელება
440
დიახ &ყველასათვის
არა ყვე&ლასათვის
შეწყვეტა
ხელახლა
&ფონურად
&წინა პლანზე
&შეჩერება
&შეჩერებული
ნამდვილად გსურთ მოქმედების შეწყვეტა?
500
&ფაილი
&დამუშავება
&ხედი
რ&ჩეულები
&ხელსაწყოები
&დახმარება
540
&გახსნა
გახსნა &შიგნით
გახსნა გა&რეთ
და&თვალიერება
და&მუშავება
გადა&რქმევა
&ასლის მოთავსება...
&გადატანა...
&წაშლა
&ფაილის დაყოფა...
&ფაილების გაერთიანება...
თ&ვისებები
კომენ&ტარი
საკონტროლო ჯამის დათვლა
Diff
საქაღალდის შექმნა
ფაილის შექმნა
გ&ამოსვლა
600
&ყველაფრის მონიშვნა
მონიშვნის გაუქმება
მონიშვნის შებრუნება
მოინიშნოს...
მოიხსნას მონიშვნა...
მონიშვნა ტიპის მიხედვით
მონიშვნის მოხსნა ტიპის მიხედვით
700
&დიდი ხატულები
&პატარა ხატულები
&სია
&დაწვრილებით
730
დაულაგებელი
ბრტყელი ხედი
&2 პანელი
&ხელსაწყოთა ზოლი
ძირეული საქაღალდის გახსნა
საქაღალდიდან გასვლა
საქაღალდეების ისტორია...
&განახლება
750
დაარქივების ზოლი
ძირითადი ზოლი
დიდი ღილაკები
წარწერები ღილაკებზე
800
&საქაღალდის რჩეულებში დამატება როგორც
სანიშნე
900
&გამართვა...
&წარმადობის შემოწმება
960
სარჩევი...
7-Zip-ის შესახებ...
1003
მდებარეობა
სახელი
გაფართოება
საქაღალდე
ზომა
შეკუმშულის ზომა
ატრიბუტები
შექმნილია
გახსნილია
შეცვლილია
უწყვეტი
კომენტარი
დაშიფრულია
დაყოფა სანამ
დაყოფა შემდეგ
ლექსიკონი
CRC
ტიპი
ანტი
მეთოდი
სისტემა
ფაილური სისტემა
მომხმარებელი
ჯგუფი
ბლოკი
კომენტარი
მდებარეობა
მდებარეობის თავსართი
საქაღალდეები
ფაილები
ვერსია
ტომი
მრავალტომიანი
წანაცვლება
ბმულები
ბლოკები
ტომები

64 ბიტი
Big-endian
CPU
ფიზიკური ზომა
სათაურების ზომა
საკონტროლო ჯამი
თვისებები
ვირტუალური მისამართი
ID
მოკლე სახელი
შემქმნელი პროგრამა
სექტორის ზომა
რეჟიმი
ბმული
შეცდომა
სრული მოცულობა
თავისუფალი სივრცე
კლასტერის ზომა
წარწერა
ადგილობრივი სახელი
მომწოდებელი
2100
გამართვა
ენები
ენა:
რედაქტორი
რედაქტორი:
&Diff:
2200
სისტემა
ასოცირება 7-Zip-თან:
2301
7-Zip-ის გარსის კონტექსტურ მენიუში ჩადგმა
კასკადური კონტექსტური მენიუ
კონტექსტური მენიუს შემადგენლობა:
2320
<საქაღალდე>
<არქივი>
არქივის გახსნა
ფაილების ამოღება...
არქივში ჩამატება...
არქივის შემოწმება
ამოღება აქ
ამოღება {0}-ში
{0}-ში ჩამატება
შეკუმშვა და ელფოსტით გაგზავნა...
{0}-ში შეკუმშვა და ელფოსტით გაგზავნა
2400
საქაღალდეები
&მუშაობის საქაღალდე
&სისტემური დროებითი საქაღალდე
&მიმდინარე
&მითითებული:
გამოიყენება ცვლადი მეხსიერების მოწყობილობებისთვის
მიუთითეთ მდებარეობა დროებითი არქივებისათვის.
2500
გამართვა
".." ელემენტის ჩვენება
ფაილთა ნამდვილი ხატულების ჩვენება
სისტემური მენიუს ჩვენება
კურსორი &მთელ სტრიქონზე
ჩვენება &ცხრილის სახით
ელემენტთა გახსნა ერთი წკაპით
მონიშნვის &ალტერნატიული რეჟიმი
&დიდი მეხსიერების ბლოკების გამოყენება
2900
7-Zip-ის შესახებ
7-Zip არის თავისუფლად გავრცელებადი პროგრამული უზრუნველყოფა.
3000
შეუძლებელია საჭირო ზომის მეხსიერების გამოყოფა
შეცდომები არ მოიძებნა
მონიშნულია {0} ობიექტი
ვერ მოხერხდა '{0}' საქაღალდეს შექმნა
ამ ტიპის არქივისათვის ცვლილების ოპერაცია ხელმიუწვდომელია.
'{0}' ფაილის არქივად გახსნა ვერ მოხერხდა
'{0}' დაშიფრული არქივის გახსნა ვერ მოხერხდა. არასწორი პაროლი?
არქივთა ამ სახეობის გახსნა შეუძლებელია
ფაილი '{0}' უკვე არსებობს
ფაილი '{0}' შეიცვალა.\nგნებავთ მისი არქივში განახლება?
შეუძლებელია \n'{0}'-ის განახლება
შეუძლებელია რედაქტორის გაშვება.
ფაილი შესაძლოა აღმოჩნდეს ვირუსი (სახელი შეიცავს ძალიან ბევრ თავმოყრილ ჰარს).
შეუძლებელია გრძელ-სახელიანი საქაღალდიდან ქმედების შესრულება
უნდა აირჩიოთ ერთი ფაილი
უნდა აირჩიოთ ერთი ან მეტი ფაილი
მეტისმეტად ბევრი ელემენტი
3300
მიმდინარეობს ამოღება
მიმდინარეობს შეკუმშვა
მიმდინარეობს შემოწმება
მიმდინარეობს გახსნა...
მიმდინარეობს ამოკითხვა...
3400
ამოღება
ა&მოღება:
მიუთითეთ ადგილი ამოსაღები ფაილებისათვის.
3410
მდებარეობა
სრული მდებარეობა
მდებარეობის გარეშე
3420
ზედგადაწერა
ზედგადაწერის დასტური
ზედგადაწერა დასრურის გარეშე
არსებული ფაილების გამოტოვება
ავტო-გადარქმევა
არსებული ფაილების ავტო-გადარქმევა
3500
ფაილის ზედგადაწერის დასტური
საქაღალდე უკვე შეიცავს დამუშავებულ ფაილს.
გსურთ ჩაანაცვლოთ არსებული ფაილი
ახალი ფაილით?
{0} ბაიტი
ა&ვტო-გადარქმევა
3700
შეკუმშვის შეუთავსებადი მეთოდი '{0}'-თვის.
მონაცემების შეცდომა '{0}'-ში. ფაილი დაზიანებულია.
CRC-ის შეცდომა '{0}'-ში. ფაილი დაზიანებულია.
მონაცემების შეცდომა დაშიფრულ ფაილში '{0}'. არასწორი პაროლი?
CRC ჩაიშალა დაშიფრულ ფაილში '{0}'. არასწორი პაროლი?
3800
პაროლის შეყვანა
შეიყვანეთ პაროლი:
პაროლი ხელახლა:
პაროლის &ჩვენება
პაროლები არ დაემთხვა
პაროლად შეიყვანეთ მხოლოდ ლათინური ასოები, ციფრები და განსაკუთერებული სიმბოლოები (!, #, $, ...)
პაროლი მეტისმეტად გრძელია
პაროლი
3900
გასული დრო:
დარჩენილი დრო:
ჯამური ზომა:
სიჩქარე:
დამუშავებული:
შეკუმშვის დონე:
შეცდომა:
არქივები:
4000
არქივში ჩამატება
&არქივი:
&განახლების რეჟიმი:
არქივის &ფორმატი:
შეკუმშვის &დონე:
შეკუმშვის &მეთოდი:
&ლექსიკონის ზომა:
&სიტყვის ზომა:
უწყვეტი ბლოკის ზომა:
CPU ნაკადების ოდენობა:
&პარამეტრები:
დამატებითი
შეიქმნას SF&X არქივი
გაზიარებული ფაილების შეკუმშვა
დაშიფრვა
დაშიფრვის მეთოდი:
ფაილთა &სახელების დაშიფრვა
მეხსიერება შეკუმშვისათვის:
მეხსიერება ამოღებისათვის:
4050
შეკუმშვის გარეშე
უსწრაფესი
სწრაფი
ჩვეულებრივი
მაღალი
უმაღლესი
4060
ფაილთა დამატება და შეცვლა
ფაილთა განახლება და დამატება
ფაილთა განახლება
ფაილთა სინქრონიზება
4070
დათვალიერება
ყველა ფაილი
წყვეტილი
უწყვეტი
6000
ასლის აღება
გადატანა
ასლის მოთავსება:
გადატანა:
ასლის აღება...
გადატანა...
გადარქმევა...
აირჩიეთ დანიშნულების საქაღალდე.
ქმედება შუთავსებელია მიმდინარე საქაღალდესთან.
ფაილის ან საქაღალდის გადარქმევის შეცდომა
ფაილის ასლის შექმნის დასტური
ნადმვილად გსურთ ფაილების არქივში ჩამატება
6100
ფაილის წაშლის თანხმობა
საქაღალდის წაშლის თანხმობა
რამდენიმე ფაილის წაშლის თანხმობა
დარწმუნებული ხართ, რომ გინდათ წაშალოთ '{0}'?
დარწმუნებული ხართ, რომ გინდათ წაშალოთ '{0}' საქაღალდე და მთელი მისი შიგთავსი?
დარწმუნებული ხართ, რომ გინდათ წაშალოთ {0} ელემენტები?
იშლება...
ფაილის ან საქაღალდის წაშლის შეცდომა
სისტემას არ შეუძლია გადაიტანოს სანაგვე ყუთში ფაილი მეტისმეტად გრძელი მისამართით.
6300
საქაღალდის შექმნა
ფაილის შექმნა
საქაღალდის სახელი:
ფაილის სახელი:
ახალი საქაღალდე
ახალი ფაილი
შეცდომა საქაღალდის შექმნისას
შეცდომა ფაილის შექმნისას
6400
კომენტარი
&კომენტარი:
მონიშვნა
მონიშვნის გაუქმება
ნიღაბი:
6600
თვისებები
საქაღალდეთა ისტორია
დიაგნოსტიკური შეტყობინება
შეტყობინება
7100
კომპიუტერი
ქსელი
დოკუმენტები
სისტემა
7200
დამატება
ამოღება
შემოწმება
ასლი
გადატანა
წაშლა
ცნობები
7300
ფაილის დაყოფა
&დაიყოფა:
დაიყოს &ტომებად, ზომით (ბაიტებში):
დაყოფა...
დაყოფის დასტური
ნამდვილად გსურთ ფაილის დაყოფა {0} ნაწილად?
ნაწილის ზომა უნდა იყოს საწყისი ფაილის ზომაზე ნაკლები
ნაწილის ზომა მიუღებელია
მითითებული ნაწილის ზომა: {0} ბაიტი.\nნამდვილად გსურთ ასეთი ზომის ნაწილებას დაყოთ ფაილი?
7400
ფაილების შეერთება
&შეერთება:
შეერთება...
მონიშნეთ დაყოფილი ფაილის მხოლოდ პირველი ნაწილი
როგორც ჩანს, ფაილი არ წარმოადგენს დაყოფილი ფაილის ნაწილს
ვერ მოიზებნა დაყოფილი ფაილის ერთზე მეტი ნაწილი
7500
საკონტროლო ჯამის დათვლა...
საკონტრლო ჯამის შესახებ
CRC ჯამი მონაცემთათვის:
CRC ჯამი სახელთა და მონაცემთათვის:
7600
წარმადობის შემოწმება
გამოყენებული მეხსიერება:
შეკუმშვა
გაშლა
შეფასება
საერთო შეფასება
მიმდინარე
შემაჯამებელი
CPU დატვირთვა
შეფას./დატვირთვა
გატარება:
