To configure `patch' for DJGPP, issue these commands from the
`patch' source directory:

	pc\djgpp\configure
	make

To build `patch' in a directory other than where the sources are,
add a parameter that specifies the source directory, e.g.:

    e:\src\patch\pc\djgpp\configure e:/src/patch

You MUST use forward slashes to specify the source directory.

Running configure.bat requires a port of `sed'.
You can find one on the usual DJGPP archive sites.


Thanks to <PERSON> <<EMAIL>> for
suggestions and ideas for this DJGPP port.
