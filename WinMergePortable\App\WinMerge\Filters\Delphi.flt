## This is a directory/file filter template for WinMerge
name: Delphi filter
desc: View only files .PAS and files .DFM

## Select if filter is inclusive or exclusive
## Inclusive (loose) filter lets through all items not matching rules
## Exclusive filter lets through only items that match to rule
## include or exclude
def: include

## Filters for filenames begin with f:
## Filters for directories begin with d:
## (Inline comments begin with " ##" and extend to the end of the line)

f: \.^~		## EXCLUDE temporary files
f: \.dcu$ 	## EXCLUDE Delphi compiled unit
f: \.exe$ 	## EXCLUDE Exe file
f: \.cfg$ 	## EXCLUDE configuration file
f: \.dsk$ 	## EXCLUDE File of internal information of the project
f: \.dof$ 	## EXCLUDE Delphi options file
f: \.ddp$ 	## EXCLUDE Delphi diagram portfolio file
f: \.db$ 	## EXCLUDE File Paradox
f: \.ims$ 	## EXCLUDE Icon file, normally created with IconForge
f: \.bak$ 	## EXCLUDE Backup file made with WinMerge

## f: \.dfm$ ## Delphi Form
## f: \.pas$ ## Delphi source
## f: \.dpr$ ## Delphi Project
## f: \.dpk$ ## Delphi Package
## f: \.bpg$ ## Delphi Package Group
## f: \.bpl$ ## Delphi Package Library

## d: \\subdir$ ## Filter for directory