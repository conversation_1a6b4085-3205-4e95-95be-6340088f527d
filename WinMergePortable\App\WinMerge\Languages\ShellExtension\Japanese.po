# This file is part from WinMerge <https://winmerge.org/>
# Released under the "GNU General Public License"
#
# ID line follows -- this is updated by SVN
# $Id$
#
msgid ""
msgstr ""
"Project-Id-Version: WinMerge Shell Extension\n"
"Report-Msgid-Bugs-To: https://bugs.winmerge.org/\n"
"POT-Creation-Date: \n"
"PO-Revision-Date: \n"
"Last-Translator: \n"
"Language-Team: Japanese <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-Basepath: ../../ShellExtension/Languages\n"
"Language: ja\n"
"X-Generator: Poedit 3.0\n"

#. AFX_TARG_*
#: ShellExtension.rc:19
#, c-format
msgid "AFX_TARG_ENU"
msgstr "AFX_TARG_JPN"

#. LANGUAGE, SUBLANGUAGE
#: ShellExtension.rc:20
#, c-format
msgid "LANG_ENGLISH, SUBLANG_ENGLISH_US"
msgstr "LANG_JAPANESE, SUBLANG_DEFAULT"

#. Codepage
#: ShellExtension.rc:21
#, c-format
msgid "1252"
msgstr "65001"

#: ShellExtension.rc:111
#, c-format
msgid "ShellExtension"
msgstr "ShellExtension"

#: ShellExtension.rc:112
#, c-format
msgid "&WinMerge"
msgstr "&WinMerge"

#: ShellExtension.rc:113
#, c-format
msgid "&Compare"
msgstr "比較(&C)"

#: ShellExtension.rc:114
#, c-format
msgid "Compare&..."
msgstr "比較&..."

#: ShellExtension.rc:115
#, c-format
msgid "Select &Left"
msgstr "左側として選択(&L)"

#: ShellExtension.rc:116
#, c-format
msgid "Select &Middle"
msgstr "中央として選択(&M)"

#: ShellExtension.rc:117
#, c-format
msgid "Re-select &Left"
msgstr "左側として再選択(&L)"
