// ==UserScript==
// @name         Yahoo新聞網址收集器
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  收集Yahoo新聞頁面上的特定格式網址
// <AUTHOR>
// @match        https://tw.news.yahoo.com/*
// @grant        none
// ==/UserScript==

(function() {
    'use strict';

    let collectedUrls = new Set();
    let isCollecting = false;
    let scrollAttempts = 0;
    const maxScrollAttempts = 20;
    const targetCount = 15;

    // 創建UI
    function createUI() {
        const ui = document.createElement('div');
        ui.id = 'url-collector-ui';
        ui.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            width: 400px;
            max-height: 600px;
            background: white;
            border: 2px solid #333;
            border-radius: 8px;
            padding: 15px;
            z-index: 10000;
            font-family: Arial, sans-serif;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            overflow-y: auto;
        `;

        ui.innerHTML = `
            <div style="margin-bottom: 15px;">
                <h3 style="margin: 0 0 10px 0; color: #333;">Yahoo新聞網址收集器</h3>
                <button id="start-collect" style="background: #007bff; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">開始收集</button>
                <button id="stop-collect" style="background: #dc3545; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; margin-left: 10px;">停止收集</button>
                <div id="status" style="margin-top: 10px; font-weight: bold; color: #666;">準備就緒</div>
            </div>
            <div id="url-list" style="border-top: 1px solid #ddd; padding-top: 15px;">
                <h4 style="margin: 0 0 10px 0; color: #333;">收集到的網址 (<span id="url-count">0</span>/${targetCount}):</h4>
                <div id="urls" style="max-height: 400px; overflow-y: auto;"></div>
            </div>
        `;

        document.body.appendChild(ui);

        // 綁定事件
        document.getElementById('start-collect').addEventListener('click', startCollection);
        document.getElementById('stop-collect').addEventListener('click', stopCollection);
    }

    // 檢查網址是否符合格式（.html前有9個數字）
    function isValidUrl(url) {
        const pattern = /\/\d{9}\.html$/;
        return pattern.test(url);
    }

    // 收集頁面上的網址
    function collectUrls() {
        const links = document.querySelectorAll('a[href*="tw.news.yahoo.com"]');
        let newUrlsFound = 0;

        links.forEach(link => {
            const href = link.href;
            if (isValidUrl(href) && !collectedUrls.has(href)) {
                collectedUrls.add(href);
                newUrlsFound++;
                addUrlToUI(href);
            }
        });

        updateStatus(`找到 ${newUrlsFound} 個新網址，總計 ${collectedUrls.size} 個`);
        document.getElementById('url-count').textContent = collectedUrls.size;

        return newUrlsFound;
    }

    // 添加網址到UI
    function addUrlToUI(url) {
        const urlsContainer = document.getElementById('urls');
        const urlElement = document.createElement('div');
        urlElement.style.cssText = `
            margin-bottom: 8px;
            padding: 8px;
            background: #f8f9fa;
            border-radius: 4px;
            border-left: 3px solid #007bff;
        `;

        const link = document.createElement('a');
        link.href = url;
        link.textContent = url;
        link.style.cssText = `
            color: #007bff;
            text-decoration: none;
            word-break: break-all;
            font-size: 12px;
        `;
        link.addEventListener('click', function(e) {
            e.preventDefault();
            window.location.href = url; // 在同一頁面打開
        });

        urlElement.appendChild(link);
        urlsContainer.appendChild(urlElement);
    }

    // 更新狀態
    function updateStatus(message) {
        document.getElementById('status').textContent = message;
    }

    // 滾動頁面
    function scrollPage() {
        return new Promise(resolve => {
            const scrollHeight = document.documentElement.scrollHeight;
            window.scrollTo(0, scrollHeight);
            
            setTimeout(() => {
                const newScrollHeight = document.documentElement.scrollHeight;
                resolve(newScrollHeight > scrollHeight);
            }, 2000); // 等待2秒讓內容加載
        });
    }

    // 隨機點擊一個連結
    function clickRandomLink() {
        const links = document.querySelectorAll('a[href*="tw.news.yahoo.com"]');
        const validLinks = Array.from(links).filter(link => isValidUrl(link.href));
        
        if (validLinks.length > 0) {
            const randomLink = validLinks[Math.floor(Math.random() * validLinks.length)];
            updateStatus('頁面網址重複，隨機點擊一個連結...');
            setTimeout(() => {
                randomLink.click();
            }, 1000);
            return true;
        }
        return false;
    }

    // 開始收集
    async function startCollection() {
        if (isCollecting) return;
        
        isCollecting = true;
        scrollAttempts = 0;
        updateStatus('開始收集網址...');

        while (isCollecting && collectedUrls.size < targetCount && scrollAttempts < maxScrollAttempts) {
            // 收集當前頁面的網址
            const newUrls = collectUrls();
            
            if (collectedUrls.size >= targetCount) {
                updateStatus(`收集完成！共收集到 ${collectedUrls.size} 個網址`);
                break;
            }

            // 如果沒有找到新網址，嘗試點擊隨機連結
            if (newUrls === 0) {
                if (clickRandomLink()) {
                    // 等待頁面加載
                    await new Promise(resolve => setTimeout(resolve, 3000));
                    continue;
                }
            }

            // 滾動頁面
            updateStatus(`滾動頁面中... (${scrollAttempts + 1}/${maxScrollAttempts})`);
            const hasNewContent = await scrollPage();
            scrollAttempts++;

            if (!hasNewContent && newUrls === 0) {
                updateStatus('沒有更多內容，嘗試點擊隨機連結...');
                if (!clickRandomLink()) {
                    updateStatus('無法找到更多連結，收集結束');
                    break;
                }
                await new Promise(resolve => setTimeout(resolve, 3000));
            }
        }

        if (collectedUrls.size >= targetCount) {
            updateStatus(`✅ 收集完成！共收集到 ${collectedUrls.size} 個網址`);
        } else {
            updateStatus(`⚠️ 收集結束，共收集到 ${collectedUrls.size} 個網址`);
        }

        isCollecting = false;
    }

    // 停止收集
    function stopCollection() {
        isCollecting = false;
        updateStatus('收集已停止');
    }

    // 初始化
    function init() {
        // 等待頁面加載完成
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', createUI);
        } else {
            createUI();
        }
    }

    init();
})();
