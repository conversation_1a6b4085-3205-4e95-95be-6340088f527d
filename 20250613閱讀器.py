import tkinter as tk
from tkinter import ttk, filedialog, messagebox, font
import fitz  # PyMuPDF
from PIL import Image, ImageTk
import io

class ResizableTextBox:
    def __init__(self, canvas, x, y, width=200, height=100, proofreading_mode=None, move_distance_func=None, add_undo_record=None):
        self.canvas = canvas
        self.x = x
        self.y = y
        self.initial_x = x  # 保存初始X座標
        self.initial_y = y  # 保存初始Y座標
        self.width = width
        self.height = height
        self.min_size = 20
        self.border_width = 1
        self.selected = False
        self.dragging = False
        self.resizing = False
        self.resize_direction = None
        self.drag_start_x = 0
        self.drag_start_y = 0
        self.last_cursor_x = None
        self.last_cursor_y = None
        self.current_cursor = ''
        self.last_detected_cursor = ''
        self.hysteresis_buffer = 8
        self.last_motion_id = None
        self.proofreading_mode = proofreading_mode
        self.move_distance_func = move_distance_func
        self.add_undo_record = add_undo_record
        self.external_buffer = 10
        self.current_font_family = "標楷體"
        self.current_font_size = 12
        self.red_char_index = None
        
        self.frame = tk.Frame(canvas, bg='white', relief='solid', bd=self.border_width)
        color = 'red' if proofreading_mode and proofreading_mode() else 'gray'
        self.frame.configure(highlightthickness=2, highlightcolor=color, highlightbackground=color, bd=0)
        
        self.text_widget = tk.Text(self.frame, wrap=tk.WORD, font=('標楷體', 12), bd=0)
        
        scrollbar = ttk.Scrollbar(self.frame, orient='vertical', command=self.text_widget.yview)
        self.text_widget.configure(yscrollcommand=scrollbar.set)
        
        self.text_widget.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')
        
        self.window_id = canvas.create_window(x, y, window=self.frame, anchor='nw', 
                                            width=width, height=height)
        
        self.frame.update_idletasks()
        self.width = self.frame.winfo_width() if self.frame.winfo_width() > 1 else width
        self.height = self.frame.winfo_height() if self.frame.winfo_height() > 1 else height
        self.canvas.itemconfig(self.window_id, width=self.width, height=self.height)
        
        self.frame.bind('<Button-1>', self.on_click)
        self.frame.bind('<B1-Motion>', self.on_drag)
        self.frame.bind('<ButtonRelease-1>', self.on_release)
        self.frame.bind('<Motion>', self.on_motion)
        self.frame.bind('<Enter>', self.on_enter)
        # 將文字框的右鍵事件轉發到畫布，並阻止事件繼續傳播
        def forward_right_click(event):
            self.canvas.event_generate('<Button-3>', x=event.x_root, y=event.y_root)
            
            def clear_focus_and_selection():
                self.canvas.focus_set()
                self.text_widget.tag_remove("sel", "1.0", "end")

            # 使用 'after' 來確保此操作在預設事件處理完成後運行
            self.canvas.after(1, clear_focus_and_selection)
            return "break"
        
        self.frame.bind('<Button-3>', forward_right_click)
        self.text_widget.bind('<Button-3>', forward_right_click)
        
        for widget in [self.frame, self.text_widget]:
            widget.bind('<Control-Button-1>', self.on_move_click)
            widget.bind('<Control-B1-Motion>', self.on_move_drag)
            widget.bind('<Control-ButtonRelease-1>', self.on_move_release)
        
        self.text_widget.bind('<Button-1>', self.on_text_click)
        self.text_widget.bind('<<Modified>>', self.on_text_modified)
        
        self.frame.after(50, self.update_cursor)
        
        self.text_widget.tag_configure("red_char", foreground="red")

    def update_cursor(self):
        self.frame.config(cursor='')
        self.current_cursor = ''
        self.last_detected_cursor = ''

    def on_enter(self, event):
        cursor = self.get_cursor_for_position(event.x, event.y)
        if cursor != self.current_cursor:
            self.frame.config(cursor=cursor)
            self.current_cursor = cursor
            self.last_detected_cursor = cursor

    def on_motion(self, event):
        if not self.resizing and not self.dragging:
            if self.last_motion_id is not None:
                try:
                    self.canvas.after_cancel(self.last_motion_id)
                except tk.TclError:
                    pass
            self.last_motion_id = self.canvas.after(10, lambda: self.update_motion(event.x, event.y))

    def update_motion(self, x, y):
        self.last_cursor_x = x
        self.last_cursor_y = y
        cursor = self.get_cursor_for_position(x, y)
        if cursor != self.current_cursor:
            self.frame.config(cursor=cursor)
            self.current_cursor = cursor
            self.last_detected_cursor = cursor if cursor else self.last_detected_cursor
        elif not cursor and not self.is_in_hysteresis_buffer(x, y):
            self.frame.config(cursor='')
            self.current_cursor = ''
        self.last_motion_id = None

    def is_in_hysteresis_buffer(self, x, y):
        if not self.last_detected_cursor:
            return False
        buffer = self.hysteresis_buffer
        edge_width_x = 25
        edge_width_y = 35
        corner_size_x = 20
        corner_size_y = 10
        external_buffer = self.external_buffer
        
        if self.last_detected_cursor in ('size_nw_se', 'size_ne_sw'):
            if ((x >= -external_buffer - buffer and x <= corner_size_x + buffer and
                 y >= -external_buffer - buffer and y <= corner_size_y + buffer) or
                (x >= self.width - corner_size_x - buffer and x <= self.width + external_buffer + buffer and
                 y >= self.height - corner_size_y - buffer and y <= self.height + external_buffer + buffer) or
                (x >= self.width - corner_size_x - buffer and x <= self.width + external_buffer + buffer and
                 y >= -external_buffer - buffer and y <= corner_size_y + buffer) or
                (x >= -external_buffer - buffer and x <= corner_size_x + buffer and
                 y >= self.height - corner_size_y - buffer and y <= self.height + external_buffer + buffer)):
                return True
        elif self.last_detected_cursor == 'size_we':
            if ((x >= -external_buffer - buffer and x <= edge_width_x + buffer and
                 corner_size_y - buffer < y < self.height - corner_size_y + buffer) or
                (x >= self.width - edge_width_x - buffer and x <= self.width + external_buffer + buffer and
                 corner_size_y - buffer < y < self.height - corner_size_y + buffer)):
                return True
        elif self.last_detected_cursor == 'size_ns':
            if ((y >= -external_buffer - buffer and y <= edge_width_y + buffer and
                 corner_size_x - buffer < x < self.width - corner_size_x + buffer) or
                (y >= self.height - edge_width_y - buffer and y <= self.height + external_buffer + buffer and
                 corner_size_x - buffer < x < self.width - corner_size_x + buffer)):
                return True
        return False

    def get_cursor_for_position(self, x, y):
        edge_width_x = 25
        edge_width_y = 35
        corner_size_x = 20
        corner_size_y = 10
        external_buffer = self.external_buffer
        
        if ((x >= -external_buffer and x <= edge_width_x and corner_size_y < y < self.height - corner_size_y) or
            (x >= self.width - edge_width_x and x <= self.width + external_buffer and corner_size_y < y < self.height - corner_size_y)):
            return 'size_we'
        if ((y >= -external_buffer and y <= edge_width_y and corner_size_x < x < self.width - corner_size_x) or
            (y >= self.height - edge_width_y and y <= self.height + external_buffer and corner_size_x < x < self.width - corner_size_x)):
            return 'size_ns'
        
        if ((x >= -external_buffer and x <= corner_size_x and y >= -external_buffer and y <= corner_size_y) or
            (x >= self.width - corner_size_x and x <= self.width + external_buffer and
             y >= self.height - corner_size_y and y <= self.height + external_buffer)):
            return 'size_nw_se'
        if ((x >= self.width - corner_size_x and x <= self.width + external_buffer and y >= -external_buffer and y <= corner_size_y) or
            (x >= -external_buffer and x <= corner_size_x and y >= self.height - corner_size_y and y <= self.height + external_buffer)):
            return 'size_ne_sw'
        
        if self.is_in_hysteresis_buffer(x, y):
            return self.last_detected_cursor
        return ''

    def on_click(self, event):
        self.selected = True
        self.drag_start_x = event.x_root
        self.drag_start_y = event.y_root
        self.resize_direction = self.get_resize_direction(event.x, event.y)
        if self.resize_direction:
            self.resizing = True
        return "break"
            
    def on_move_click(self, event):
        self.selected = True
        self.drag_start_x = event.x_root
        self.drag_start_y = event.y_root
        self.dragging = True
        return "break"
        
    def on_move_drag(self, event):
        if self.dragging:
            dx = event.x_root - self.drag_start_x
            dy = event.y_root - self.drag_start_y
            new_x = self.x + dx
            new_y = self.y + dy

            # 限制文字框不超出畫布邊界
            new_x, new_y = self.constrain_to_canvas(new_x, new_y)

            self.x = new_x
            self.y = new_y
            self.canvas.coords(self.window_id, self.x, self.y)
            self.drag_start_x = event.x_root
            self.drag_start_y = event.y_root

            # 立即更新滾動位置，實現即時跟隨
            self.ensure_visible_with_smooth_scroll()
        return "break"
            
    def on_move_release(self, event):
        self.dragging = False
        return "break"
    
    def on_text_click(self, event):
        if self.proofreading_mode and self.proofreading_mode():
            try:
                click_index = self.text_widget.index(f"@{event.x},{event.y}")
                click_index_plus_one = self.text_widget.index(f"{click_index}+1c")

                # 檢查刪除位置後面是否有空格或換行符號
                delete_end_index = click_index_plus_one
                remaining_text = self.text_widget.get(click_index_plus_one, "end-1c")
                should_move_textbox = True  # 預設會移動文字框

                if remaining_text and remaining_text[0] == ' ':
                    # 如果後面有空格，也一併刪除，但不移動文字框
                    delete_end_index = self.text_widget.index(f"{click_index_plus_one}+1c")
                    should_move_textbox = False
                elif remaining_text and remaining_text[0] == '\n':
                    # 如果後面有換行符號，也一併刪除
                    delete_end_index = self.text_widget.index(f"{click_index_plus_one}+1c")
                    should_move_textbox = True  # 刪除換行符號時仍然移動文字框

                if self.add_undo_record:
                    original_text = self.text_widget.get("1.0", "end-1c")
                    original_x = self.x
                    original_y = self.y
                    deleted_text = self.text_widget.get("1.0", delete_end_index)
                    remaining_text_after_delete = self.text_widget.get(delete_end_index, "end-1c")
                    undo_record = {
                        'textbox': self,
                        'action': 'proofreading_delete',
                        'original_text': original_text,
                        'original_x': original_x,
                        'original_y': original_y,
                        'deleted_text': deleted_text,
                        'remaining_text': remaining_text_after_delete,
                        'click_index': delete_end_index,
                        'red_char_index': self.red_char_index
                    }
                    self.add_undo_record(undo_record)

                self.text_widget.delete("1.0", delete_end_index)
                self.text_widget.mark_set("insert", "1.0")

                # 只有在沒有刪除空格的情況下才移動文字框
                if should_move_textbox:
                    self.move_textbox_down()

                self.update_red_char()
                # 關鍵：在刪除文字並完成所有操作後，立即清除任何可能產生的選取狀態
                self.text_widget.tag_remove("sel", "1.0", "end")
                return "break"
            except Exception as e:
                print(f"校對模式錯誤: {e}")
        return None
    
    def move_textbox_down(self):
        try:
            move_distance = self.get_move_distance()
            new_y = self.y + move_distance

            # 限制移動後的位置不超出畫布邊界
            constrained_x, constrained_y = self.constrain_to_canvas(self.x, new_y)

            # 如果Y座標被限制了，計算實際移動的距離
            actual_move_distance = constrained_y - self.y

            self.y = constrained_y
            self.canvas.coords(self.window_id, self.x, self.y)

            # 同步滾動畫布，使用實際移動距離
            scroll_region = self.canvas.bbox("all")
            if scroll_region and actual_move_distance > 0:
                current_scroll = self.canvas.yview()[0]  # 當前滾動位置 (0-1之間)
                canvas_scrollregion_height = scroll_region[3]  # 整個滾動區域的高度
                scroll_ratio = actual_move_distance / canvas_scrollregion_height  # 計算應該滾動的比例
                new_scroll = min(1.0, current_scroll + scroll_ratio)  # 確保不超過最大滾動範圍
                self.canvas.yview_moveto(new_scroll)
        except Exception as e:
            print(f"移動文字框錯誤: {e}")
    
    def get_move_distance(self):
        if self.move_distance_func:
            return self.move_distance_func()
        return 20
        
    def restore_state(self, text, x, y, red_char_index=None):
        try:
            self.text_widget.delete("1.0", "end")
            self.text_widget.insert("1.0", text)
            self.x = x
            self.y = y
            self.canvas.coords(self.window_id, x, y)
            self.text_widget.mark_set("insert", "1.0")
            self.red_char_index = red_char_index
            self.update_red_char()
        except Exception as e:
            print(f"恢復狀態錯誤: {e}")
        
    def on_drag(self, event):
        if self.resizing and self.resize_direction:
            dx = event.x_root - self.drag_start_x
            dy = event.y_root - self.drag_start_y
            new_width = self.width
            new_height = self.height
            new_x = self.x
            new_y = self.y
            if 'left' in self.resize_direction:
                new_width = max(self.min_size, self.width - dx)
                new_x = self.x + dx
            elif 'right' in self.resize_direction:
                new_width = max(self.min_size, self.width + dx)
            if 'top' in self.resize_direction:
                new_height = max(self.min_size, self.height - dy)
                new_y = self.y + dy
            elif 'bottom' in self.resize_direction:
                new_height = max(self.min_size, self.height + dy)

            # 限制調整大小後的位置不超出畫布
            new_x, new_y = self.constrain_to_canvas(new_x, new_y)

            self.width = new_width
            self.height = new_height
            self.x = new_x
            self.y = new_y
            self.canvas.coords(self.window_id, self.x, self.y)
            self.canvas.itemconfig(self.window_id, width=self.width, height=self.height)
            self.drag_start_x = event.x_root
            self.drag_start_y = event.y_root
        return "break"
            
    def on_release(self, event):
        self.dragging = False
        self.resizing = False
        self.resize_direction = None
        
    def get_resize_direction(self, x, y):
        edge_width_x = 25
        edge_width_y = 35
        external_buffer = self.external_buffer
        direction = []
        if y >= -external_buffer and y <= edge_width_y:
            direction.append('top')
        elif y >= self.height - edge_width_y and y <= self.height + external_buffer:
            direction.append('bottom')
        if x >= -external_buffer and x <= edge_width_x:
            direction.append('left')
        elif x >= self.width - edge_width_x and x <= self.width + external_buffer:
            direction.append('right')
        return '_'.join(direction) if direction else None
        
    def set_font(self, font_family, font_size):
        try:
            if isinstance(font_size, str):
                font_size = float(font_size)
            new_font = font.Font(family="標楷體", size=int(font_size))
            self.text_widget.config(font=new_font)
            self.current_font_family = "標楷體"
            self.current_font_size = font_size
            self.update_red_char()
        except Exception as e:
            print(f"字體設定錯誤: {e}")
            try:
                default_font = font.Font(family="標楷體", size=12)
                self.text_widget.config(font=default_font)
            except:
                pass

    def set_red_char(self, index):
        try:
            old_index = self.red_char_index
            self.red_char_index = index
            if self.add_undo_record:
                undo_record = {
                    'textbox': self,
                    'action': 'set_red_char',
                    'old_red_char_index': old_index,
                    'new_red_char_index': index
                }
                self.add_undo_record(undo_record)
            self.update_red_char()
        except Exception as e:
            print(f"設置紅色字錯誤: {e}")

    def update_red_char(self):
        try:
            self.text_widget.tag_remove("red_char", "1.0", "end")
            if self.red_char_index is not None:
                text = self.text_widget.get("1.0", "end-1c")
                if self.red_char_index >= 0 and self.red_char_index < len(text):
                    start_index = f"1.0 + {self.red_char_index} chars"
                    end_index = f"1.0 + {self.red_char_index + 1} chars"
                    self.text_widget.tag_add("red_char", start_index, end_index)
        except Exception as e:
            print(f"更新紅色字錯誤: {e}")

    def on_text_modified(self, event):
        if self.text_widget.edit_modified():
            self.update_red_char()
            self.text_widget.edit_modified(False)

    def set_border_color(self, color):
        self.frame.configure(highlightcolor=color, highlightbackground=color)

    def reset_to_initial_position(self):
        """重置文字框位置到初始座標"""
        try:
            self.x = self.initial_x
            self.y = self.initial_y
            self.canvas.coords(self.window_id, self.x, self.y)
        except Exception as e:
            print(f"重置文字框位置錯誤: {e}")

    def reset_to_initial_y(self):
        """重置文字框Y座標到初始位置，保持X座標不變"""
        try:
            self.y = self.initial_y
            self.canvas.coords(self.window_id, self.x, self.y)
        except Exception as e:
            print(f"重置文字框Y座標錯誤: {e}")

    def constrain_to_canvas(self, x, y):
        """限制文字框位置在畫布範圍內"""
        try:
            # 獲取畫布的滾動區域
            scroll_region = self.canvas.bbox("all")
            if not scroll_region:
                return x, y

            # 畫布的實際範圍
            canvas_left = 0
            canvas_top = 0
            canvas_right = scroll_region[2]
            canvas_bottom = scroll_region[3]

            # 限制X座標
            min_x = canvas_left
            max_x = canvas_right - self.width
            x = max(min_x, min(x, max_x))

            # 限制Y座標
            min_y = canvas_top
            max_y = canvas_bottom - self.height
            y = max(min_y, min(y, max_y))

            return x, y
        except Exception as e:
            print(f"限制畫布範圍錯誤: {e}")
            return x, y

    def ensure_visible_with_smooth_scroll(self):
        """確保文字框在可視區域內，並平滑滾動到合適位置"""
        try:
            # 取得畫布的可視區域和整體區域
            canvas_width = self.canvas.winfo_width()
            canvas_height = self.canvas.winfo_height()
            scroll_region = self.canvas.bbox("all")
            if not scroll_region:
                return

            total_width = scroll_region[2] - scroll_region[0]
            total_height = scroll_region[3] - scroll_region[1]

            # 計算文字方塊的可視範圍
            box_visible_left = self.x
            box_visible_right = self.x + self.width
            box_visible_top = self.y
            box_visible_bottom = self.y + self.height

            # 取得當前卷軸位置
            current_x = self.canvas.xview()[0]
            current_y = self.canvas.yview()[0]

            # 計算目標卷軸位置，使用更平滑的計算方式
            margin = 50  # 邊緣緩衝區
            smooth_factor = 0.3  # 平滑係數，溫和的滾動

            # 水平方向
            if box_visible_right > (current_x * total_width + canvas_width - margin):
                # 如果文字方塊右側超出可視區域
                target_x = (box_visible_right - canvas_width + margin) / total_width
                target_x = min(1, max(0, target_x))
                # 平滑滾動
                new_x = current_x + (target_x - current_x) * smooth_factor
                self.canvas.xview_moveto(new_x)
            elif box_visible_left < (current_x * total_width + margin):
                # 如果文字方塊左側超出可視區域
                target_x = (box_visible_left - margin) / total_width
                target_x = min(1, max(0, target_x))
                # 平滑滾動
                new_x = current_x + (target_x - current_x) * smooth_factor
                self.canvas.xview_moveto(new_x)

            # 垂直方向
            if box_visible_bottom > (current_y * total_height + canvas_height - margin):
                # 如果文字方塊底部超出可視區域
                target_y = (box_visible_bottom - canvas_height + margin) / total_height
                target_y = min(1, max(0, target_y))
                # 平滑滾動
                new_y = current_y + (target_y - current_y) * smooth_factor
                self.canvas.yview_moveto(new_y)
            elif box_visible_top < (current_y * total_height + margin):
                # 如果文字方塊頂部超出可視區域
                target_y = (box_visible_top - margin) / total_height
                target_y = min(1, max(0, target_y))
                # 平滑滾動
                new_y = current_y + (target_y - current_y) * smooth_factor
                self.canvas.yview_moveto(new_y)

        except Exception as e:
            print(f"滾動跟隨錯誤: {e}")

class PDFReader:
    def __init__(self, root):
        self.root = root
        self.root.title("PDF閱讀器")
        self.root.geometry("1200x800")
        
        self.pdf_document = None
        self.current_page = 0
        self.zoom_level = 1.0
        self.text_boxes = []
        self.proofreading_mode = False
        self.undo_history = []
        self.max_undo_history = 50
        self.screen_dpi = root.winfo_fpixels('1i')
        self.font_size_var = tk.StringVar(value="12")
        self.font_size_var2 = tk.StringVar(value="16")
        self.active_font_var = self.font_size_var  # 追蹤當前使用的字體大小

        # 記錄每個頁面的文字框位置和滾動位置
        self.page_positions = {}  # {page_number: {'textboxes': [...], 'scroll_x': x, 'scroll_y': y}}

        self.setup_ui()
        
    def setup_ui(self):

        toolbar = ttk.Frame(self.root)
        toolbar.pack(side=tk.TOP, fill=tk.X, padx=5, pady=5)
        
        ttk.Button(toolbar, text="開啟PDF", command=self.open_pdf).pack(side=tk.LEFT, padx=2)
        
        ttk.Label(toolbar, text="頁面:").pack(side=tk.LEFT, padx=5)
        self.page_var = tk.StringVar(value="0")
        self.page_entry = ttk.Entry(toolbar, textvariable=self.page_var, width=5)
        self.page_entry.pack(side=tk.LEFT, padx=2)
        self.page_entry.bind('<Return>', self.goto_page)
        
        self.page_label = ttk.Label(toolbar, text="/ 0")
        self.page_label.pack(side=tk.LEFT, padx=2)
        
        ttk.Button(toolbar, text="上一頁", command=self.prev_page).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar, text="下一頁", command=self.next_page).pack(side=tk.LEFT, padx=2)
        
        ttk.Label(toolbar, text="縮放:").pack(side=tk.LEFT, padx=5)
        self.zoom_var = tk.StringVar(value="100")
        zoom_entry = ttk.Entry(toolbar, textvariable=self.zoom_var, width=6)
        zoom_entry.pack(side=tk.LEFT, padx=2)
        zoom_entry.bind('<Return>', self.set_zoom)
        
        ttk.Label(toolbar, text="%").pack(side=tk.LEFT)
        
        ttk.Button(toolbar, text="新增文字框", command=self.add_text_box).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar, text="刪除全部文字框", command=self.delete_all_text_boxes).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar, text="重置Y座標", command=self.reset_all_textboxes_to_initial_y).pack(side=tk.LEFT, padx=2)

        self.proofreading_button = ttk.Button(toolbar, text="校對模式: 關", command=self.toggle_proofreading_mode)
        self.proofreading_button.pack(side=tk.LEFT, padx=2)
        
        self.undo_button = ttk.Button(toolbar, text="復原 (Ctrl+Z)", command=self.undo_last_action, state='disabled')
        self.undo_button.pack(side=tk.LEFT, padx=2)
        
        ttk.Label(toolbar, text="移動距離:").pack(side=tk.LEFT, padx=5)
        self.move_distance_var = tk.StringVar(value="20")
        move_distance_entry = ttk.Entry(toolbar, textvariable=self.move_distance_var, width=6)
        move_distance_entry.pack(side=tk.LEFT, padx=2)
        ttk.Label(toolbar, text="px").pack(side=tk.LEFT)
        
        # 字體大小1輸入框
        ttk.Label(toolbar, text="字體大小1:").pack(side=tk.LEFT, padx=5)
        font_size_entry = ttk.Entry(toolbar, textvariable=self.font_size_var, width=6)
        font_size_entry.pack(side=tk.LEFT, padx=2)
        font_size_entry.bind('<Return>', self.update_font)
        
        # 字體大小2輸入框
        ttk.Label(toolbar, text="字體大小2:").pack(side=tk.LEFT, padx=5)
        font_size_entry2 = ttk.Entry(toolbar, textvariable=self.font_size_var2, width=6)
        font_size_entry2.pack(side=tk.LEFT, padx=2)
        font_size_entry2.bind('<Return>', self.update_font)
        
        ttk.Label(toolbar, text="紅色字索引:").pack(side=tk.LEFT, padx=5)
        self.red_char_var = tk.StringVar(value="")
        red_char_entry = ttk.Entry(toolbar, textvariable=self.red_char_var, width=6)
        red_char_entry.pack(side=tk.LEFT, padx=2)
        red_char_entry.bind('<Return>', self.set_red_char)
        
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.canvas = tk.Canvas(main_frame, bg='white')
        v_scrollbar = ttk.Scrollbar(main_frame, orient='vertical', command=self.canvas.yview)
        h_scrollbar = ttk.Scrollbar(main_frame, orient='horizontal', command=self.canvas.xview)
        
        self.canvas.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        self.canvas.grid(row=0, column=0, sticky='nsew')
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')
        
        main_frame.grid_rowconfigure(0, weight=1)
        main_frame.grid_columnconfigure(0, weight=1)
        
        self.canvas.bind('<MouseWheel>', self.on_mousewheel)
        self.canvas.bind('<Button-4>', self.on_mousewheel)
        self.canvas.bind('<Button-5>', self.on_mousewheel)
        self.canvas.bind('<Control-MouseWheel>', self.on_zoom_mousewheel)
        self.canvas.bind('<Control-Button-4>', self.on_zoom_mousewheel)
        self.canvas.bind('<Control-Button-5>', self.on_zoom_mousewheel)
        self.canvas.bind('<Double-Button-1>', self.add_text_box_at_position)
        self.canvas.bind('<Button-3>', self.toggle_proofreading_mode)  # 綁定右鍵到切換校對模式
        self.root.bind('<Alt_L>', self.switch_font_var) # 綁定 Alt 鍵到切換字體
        
        self.root.bind('<Control-z>', lambda e: self.undo_last_action())
        self.root.bind('<Control-Z>', lambda e: self.undo_last_action())
        
        self.status_bar = ttk.Label(self.root, text=f"請開啟PDF檔案 | 當前字體大小: {self.font_size_var.get()} | 提示：右鍵切換字體大小")
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
        
    def add_undo_record(self, record):
        self.undo_history.append(record)
        if len(self.undo_history) > self.max_undo_history:
            self.undo_history.pop(0)
        self.undo_button.config(state='normal')
        record_count = len(self.undo_history)
        action_name = record.get('action', '未知操作')
        self.status_bar.config(text=f"已記錄操作: {action_name} (可復原操作: {record_count})")
    
    def undo_last_action(self):
        if not self.undo_history:
            messagebox.showinfo("訊息", "沒有可復原的操作")
            return
        try:
            last_record = self.undo_history.pop()
            if last_record['action'] == 'proofreading_delete':
                textbox = last_record['textbox']
                original_text = last_record['original_text']
                original_x = last_record['original_x']
                original_y = last_record['original_y']
                red_char_index = last_record.get('red_char_index')
                textbox.restore_state(original_text, original_x, original_y, red_char_index)
                self.status_bar.config(text="已復原校對模式刪除操作")
            elif last_record['action'] == 'set_red_char':
                textbox = last_record['textbox']
                old_index = last_record['old_red_char_index']
                textbox.set_red_char(old_index)
                self.status_bar.config(text="已復原紅色字設置")
            if not self.undo_history:
                self.undo_button.config(state='disabled')
                self.status_bar.config(text="沒有更多可復原的操作")
            else:
                record_count = len(self.undo_history)
                self.status_bar.config(text=f"復原成功 (剩餘可復原操作: {record_count})")
        except Exception as e:
            messagebox.showerror("錯誤", f"復原操作失敗: {str(e)}")
            if 'last_record' in locals():
                self.undo_history.append(last_record)
    
    def switch_font_var(self, event):
        # 切換字體大小變數並立即應用到所有文字框
        if self.active_font_var == self.font_size_var:
            self.active_font_var = self.font_size_var2
            font_size = self.font_size_var2.get()
            font_label = "字體大小2"
        else:
            self.active_font_var = self.font_size_var
            font_size = self.font_size_var.get()
            font_label = "字體大小1"
        
        try:
            font_size_val = float(font_size)
            if font_size_val <= 0:
                raise ValueError("字體大小必須大於0")
            updated_count = 0
            for text_box in self.text_boxes:
                text_box.set_font("標楷體", font_size_val)
                updated_count += 1
            self.status_bar.config(text=f"已切換到{font_label}: {font_size} | 已更新 {updated_count} 個文字框")
        except ValueError as e:
            self.status_bar.config(text=f"切換{font_label}失敗: {str(e)}")
        
        return "break"

    def save_current_page_positions(self):
        """保存當前頁面的文字框位置和滾動位置"""
        try:
            if not self.pdf_document:
                return

            # 保存文字框位置
            textbox_positions = []
            for text_box in self.text_boxes:
                textbox_positions.append({
                    'x': text_box.x,
                    'y': text_box.y,
                    'width': text_box.width,
                    'height': text_box.height,
                    'text': text_box.text_widget.get("1.0", "end-1c"),
                    'font_size': text_box.current_font_size,
                    'red_char_index': text_box.red_char_index
                })

            # 保存滾動位置
            scroll_x = self.canvas.xview()[0]
            scroll_y = self.canvas.yview()[0]

            # 保存到字典中
            self.page_positions[self.current_page] = {
                'textboxes': textbox_positions,
                'scroll_x': scroll_x,
                'scroll_y': scroll_y
            }
        except Exception as e:
            print(f"保存頁面位置錯誤: {e}")

    def restore_page_positions(self):
        """恢復當前頁面的文字框位置和滾動位置"""
        try:
            if not self.pdf_document or self.current_page not in self.page_positions:
                return False

            page_data = self.page_positions[self.current_page]

            # 清除現有文字框
            for text_box in self.text_boxes:
                try:
                    if hasattr(text_box, 'window_id'):
                        self.canvas.delete(text_box.window_id)
                    if hasattr(text_box, 'frame'):
                        text_box.frame.destroy()
                except Exception as e:
                    print(f"清除文字框時發生錯誤: {e}")

            self.text_boxes.clear()

            # 恢復文字框
            for textbox_data in page_data['textboxes']:
                text_box = ResizableTextBox(
                    self.canvas,
                    textbox_data['x'],
                    textbox_data['y'],
                    textbox_data['width'],
                    textbox_data['height'],
                    proofreading_mode=lambda: self.proofreading_mode,
                    move_distance_func=self.get_move_distance,
                    add_undo_record=self.add_undo_record
                )

                # 恢復文字內容和屬性
                text_box.text_widget.insert("1.0", textbox_data['text'])
                text_box.set_font("標楷體", textbox_data['font_size'])
                if textbox_data['red_char_index'] is not None:
                    text_box.red_char_index = textbox_data['red_char_index']
                    text_box.update_red_char()

                self.text_boxes.append(text_box)

            # 恢復滾動位置
            self.canvas.xview_moveto(page_data['scroll_x'])
            self.canvas.yview_moveto(page_data['scroll_y'])

            return True
        except Exception as e:
            print(f"恢復頁面位置錯誤: {e}")
            return False

    def open_pdf(self):
        file_path = filedialog.askopenfilename(
            title="選擇PDF檔案",
            filetypes=[("PDF files", "*.pdf"), ("All files", "*.*")]
        )
        if file_path:
            try:
                self.pdf_document = fitz.open(file_path)
                self.current_page = 0
                self.page_var.set("1")
                self.page_label.config(text=f"/ {len(self.pdf_document)}")
                self.zoom_level = 1.0
                self.zoom_var.set("100")
                self.display_page()
                self.undo_history.clear()
                self.undo_button.config(state='disabled')
                # 清空頁面位置記錄
                self.page_positions.clear()
                self.status_bar.config(text=f"已開啟: {file_path} | 當前字體大小: {self.active_font_var.get()}")
            except Exception as e:
                messagebox.showerror("錯誤", f"無法開啟PDF檔案: {str(e)}")
                
    def display_page(self, reset_textboxes=True):
        if not self.pdf_document:
            return
        try:
            page = self.pdf_document[self.current_page]
            mat = fitz.Matrix(self.zoom_level, self.zoom_level)
            pix = page.get_pixmap(matrix=mat)

            img_data = pix.tobytes("ppm")
            image = Image.open(io.BytesIO(img_data))

            self.photo = ImageTk.PhotoImage(image)

            self.canvas.delete("pdf_page")
            self.canvas.create_image(0, 0, anchor='nw', image=self.photo, tags="pdf_page")

            self.canvas.configure(scrollregion=self.canvas.bbox("all"))

            # 只有在需要時才重置所有文字框到初始位置
            if reset_textboxes:
                for text_box in self.text_boxes:
                    text_box.reset_to_initial_position()

        except Exception as e:
            messagebox.showerror("錯誤", f"無法顯示頁面: {str(e)}")
            
    def prev_page(self):
        if self.pdf_document and self.current_page > 0:
            # 保存當前頁面的位置
            self.save_current_page_positions()

            self.current_page -= 1
            self.page_var.set(str(self.current_page + 1))
            self.display_page()

            # 嘗試恢復之前保存的位置，如果沒有則滾動到左上角
            if not self.restore_page_positions():
                self.canvas.yview_moveto(0)  # 滾動到最上面
                self.canvas.xview_moveto(0)  # 滾動到最左邊
            
    def next_page(self):
        if self.pdf_document and self.current_page < len(self.pdf_document) - 1:
            # 保存當前頁面的位置
            self.save_current_page_positions()

            self.current_page += 1
            self.page_var.set(str(self.current_page + 1))
            self.display_page()

            # 嘗試恢復之前保存的位置，如果沒有則滾動到左上角
            if not self.restore_page_positions():
                self.canvas.yview_moveto(0)  # 滾動到最上面
                self.canvas.xview_moveto(0)  # 滾動到最左邊
            
    def goto_page(self, event=None):
        if not self.pdf_document:
            return
        try:
            page_num = int(self.page_var.get()) - 1
            if 0 <= page_num < len(self.pdf_document):
                # 保存當前頁面的位置
                self.save_current_page_positions()

                self.current_page = page_num
                self.display_page()

                # 嘗試恢復之前保存的位置，如果沒有則滾動到左上角
                if not self.restore_page_positions():
                    self.canvas.yview_moveto(0)  # 滾動到最上面
                    self.canvas.xview_moveto(0)  # 滾動到最左邊
            else:
                messagebox.showwarning("警告", "頁面號碼超出範圍")
                self.page_var.set(str(self.current_page + 1))
        except ValueError:
            messagebox.showerror("錯誤", "請輸入有效的頁面號碼")
            self.page_var.set(str(self.current_page + 1))
            
    def set_zoom(self, event=None):
        try:
            zoom_percent = float(self.zoom_var.get())
            if zoom_percent > 0:
                self.zoom_level = zoom_percent / 100.0
                self.display_page(reset_textboxes=False)  # 縮放時不重置文字框位置
                # 格式化顯示：如果是整數就不顯示小數點，否則顯示小數點
                if zoom_percent == int(zoom_percent):
                    zoom_display = f"{int(zoom_percent)}"
                else:
                    zoom_display = f"{zoom_percent:.1f}"
                self.status_bar.config(text=f"縮放已設定為 {zoom_display}% | 當前字體大小: {self.active_font_var.get()}")
            else:
                messagebox.showwarning("警告", "縮放比例必須大於0")
                # 恢復時也支持小數點顯示
                current_zoom = self.zoom_level * 100
                if current_zoom == int(current_zoom):
                    self.zoom_var.set(str(int(current_zoom)))
                else:
                    self.zoom_var.set(f"{current_zoom:.1f}")
        except ValueError:
            messagebox.showerror("錯誤", "請輸入有效的縮放比例")
            # 恢復時也支持小數點顯示
            current_zoom = self.zoom_level * 100
            if current_zoom == int(current_zoom):
                self.zoom_var.set(str(int(current_zoom)))
            else:
                self.zoom_var.set(f"{current_zoom:.1f}")
            
    def on_mousewheel(self, event):
        if event.num == 4 or event.delta > 0:
            self.canvas.yview_scroll(-1, "units")
        elif event.num == 5 or event.delta < 0:
            self.canvas.yview_scroll(1, "units")

    def on_zoom_mousewheel(self, event):
        """處理 Ctrl + 滾輪的縮放功能，支援小數點縮放"""
        if not self.pdf_document:
            return

        try:
            current_zoom = self.zoom_level * 100

            # 設定縮放步長為0.1%，實現精細縮放控制
            zoom_step = 0.1

            if event.num == 4 or event.delta > 0:
                # 向上滾動，放大
                new_zoom = current_zoom + zoom_step
            elif event.num == 5 or event.delta < 0:
                # 向下滾動，縮小
                new_zoom = current_zoom - zoom_step
            else:
                return

            # 限制縮放範圍在10%到500%之間
            new_zoom = max(10.0, min(500.0, new_zoom))

            # 四捨五入到一位小數，避免浮點數精度問題
            new_zoom = round(new_zoom, 1)

            # 更新縮放
            self.zoom_level = new_zoom / 100.0

            # 格式化顯示：如果是整數就不顯示小數點，否則顯示一位小數
            if new_zoom == int(new_zoom):
                zoom_display = str(int(new_zoom))
                self.zoom_var.set(str(int(new_zoom)))
            else:
                zoom_display = f"{new_zoom:.1f}"
                self.zoom_var.set(f"{new_zoom:.1f}")

            self.display_page(reset_textboxes=False)  # 縮放時不重置文字框位置
            self.status_bar.config(text=f"縮放已設定為 {zoom_display}% | 當前字體大小: {self.active_font_var.get()}")

        except Exception as e:
            print(f"縮放錯誤: {e}")
            
    def add_text_box(self):
        x = 50
        y = 50
        text_box = ResizableTextBox(
            self.canvas, x, y,
            proofreading_mode=lambda: self.proofreading_mode,
            move_distance_func=self.get_move_distance,
            add_undo_record=self.add_undo_record
        )
        text_box.set_font("標楷體", self.active_font_var.get())
        self.text_boxes.append(text_box)
    
    def add_text_box_at_position(self, event):
        x = self.canvas.canvasx(event.x)
        y = self.canvas.canvasy(event.y)

        # 創建文字框
        text_box = ResizableTextBox(
            self.canvas, x, y,
            proofreading_mode=lambda: self.proofreading_mode,
            move_distance_func=self.get_move_distance,
            add_undo_record=self.add_undo_record
        )

        # 限制文字框位置在畫布範圍內
        constrained_x, constrained_y = text_box.constrain_to_canvas(x, y)
        if constrained_x != x or constrained_y != y:
            text_box.x = constrained_x
            text_box.y = constrained_y
            text_box.canvas.coords(text_box.window_id, constrained_x, constrained_y)

        text_box.set_font("標楷體", self.active_font_var.get())
        self.text_boxes.append(text_box)

    def delete_all_text_boxes(self):
        """刪除所有文字框"""
        if not self.text_boxes:
            messagebox.showinfo("訊息", "目前沒有文字框可以刪除")
            return

        # 詢問用戶確認
        result = messagebox.askyesno(
            "確認刪除",
            f"確定要刪除所有 {len(self.text_boxes)} 個文字框嗎？\n此操作無法復原。",
            icon='warning'
        )

        if result:
            try:
                deleted_count = len(self.text_boxes)

                # 刪除所有文字框的視覺元素
                for text_box in self.text_boxes:
                    try:
                        # 刪除畫布上的文字框元素
                        if hasattr(text_box, 'window_id'):
                            self.canvas.delete(text_box.window_id)
                        # 銷毀文字框的 tkinter 元件
                        if hasattr(text_box, 'frame'):
                            text_box.frame.destroy()
                    except Exception as e:
                        print(f"刪除文字框時發生錯誤: {e}")

                # 清空文字框列表
                self.text_boxes.clear()

                # 清空復原歷史（因為文字框已被刪除）
                self.undo_history.clear()
                self.undo_button.config(state='disabled')

                # 更新狀態欄
                self.status_bar.config(text=f"已刪除 {deleted_count} 個文字框 | 當前字體大小: {self.active_font_var.get()}")

            except Exception as e:
                messagebox.showerror("錯誤", f"刪除文字框時發生錯誤: {str(e)}")
        else:
            self.status_bar.config(text=f"取消刪除操作 | 當前有 {len(self.text_boxes)} 個文字框")

    def reset_all_textboxes_to_initial_y(self):
        """重置所有文字框的Y座標到初始位置，保持X座標不變，並滾動卷軸到頂部和右側"""
        if not self.text_boxes:
            messagebox.showinfo("訊息", "目前沒有文字框可以重置")
            return

        try:
            reset_count = 0
            for text_box in self.text_boxes:
                text_box.reset_to_initial_y()
                reset_count += 1

            # 重置垂直卷軸到頂部
            self.canvas.yview_moveto(0)

            # 重置水平卷軸到右側
            self.canvas.xview_moveto(1.0)

            self.status_bar.config(text=f"已重置 {reset_count} 個文字框的Y座標到初始位置並滾動到頂部右側 | 當前字體大小: {self.active_font_var.get()}")

        except Exception as e:
            messagebox.showerror("錯誤", f"重置文字框Y座標時發生錯誤: {str(e)}")

    def toggle_proofreading_mode(self, event=None):
        self.proofreading_mode = not self.proofreading_mode
        if self.proofreading_mode:
            self.proofreading_button.config(text="校對模式: 開")
            new_color = 'red'
            self.status_bar.config(text=f"校對模式已開啟 | 當前字體大小: {self.active_font_var.get()}")
        else:
            self.proofreading_button.config(text="校對模式: 關")
            new_color = 'gray'
            self.status_bar.config(text=f"校對模式已關閉 | 當前字體大小: {self.active_font_var.get()}")

        for textbox in self.text_boxes:
            textbox.set_border_color(new_color)

    def get_move_distance(self):
        try:
            distance = float(self.move_distance_var.get())
            return max(0, distance)
        except ValueError:
            return 20
        
    def update_font(self, event=None):
        try:
            font_size_str = self.active_font_var.get()
            font_size = float(font_size_str)
            if font_size <= 0:
                raise ValueError("字體大小必須大於0")
            updated_count = 0
            for text_box in self.text_boxes:
                text_box.set_font("標楷體", font_size)
                updated_count += 1
            font_label = "字體大小1" if self.active_font_var == self.font_size_var else "字體大小2"
            self.status_bar.config(text=f"已更新 {updated_count} 個文字框的{font_label}為 {font_size}")
        except Exception as e:
            messagebox.showerror("錯誤", f"字體更新失敗: {str(e)}")
    
    def set_red_char(self, event=None):
        try:
            index_str = self.red_char_var.get()
            if index_str.strip() == "":
                index = None
            else:
                index = int(index_str) - 1
                if index < 0:
                    raise ValueError("索引必須大於0")
            updated_count = 0
            for text_box in self.text_boxes:
                text_box.set_red_char(index)
                updated_count += 1
            if updated_count > 0:
                self.status_bar.config(text=f"已設置 {updated_count} 個文字框的紅色字索引為 {index_str if index is not None else '無'}")
            else:
                self.status_bar.config(text="沒有文字框可設置紅色字")
        except Exception as e:
            messagebox.showerror("錯誤", f"設置紅色字失敗: {str(e)}")

def main():
    root = tk.Tk()
    app = PDFReader(root)
    root.mainloop()

if __name__ == "__main__":
    main()