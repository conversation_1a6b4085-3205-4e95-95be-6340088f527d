﻿# This file is part of Frhed <http://frhed.sourceforge.net/>
# Released under the "GNU General Public License"
#
# Translators:
# * <PERSON><PERSON><PERSON> <sawana<PERSON>@d1.dion.ne.jp>
#
# ID line follows -- this is updated by SVN
# $Id: ja.po 697 2009-06-10 11:30:30Z kimmov $
#
msgid ""
msgstr ""
"Project-Id-Version: Frhed\n"
"Report-Msgid-Bugs-To: http://sourceforge.net/tracker/?"
"group_id=13216&atid=113216\n"
"POT-Creation-Date: \n"
"PO-Revision-Date: 2018-05-03 00:00+0900\n"
"Last-Translator: <PERSON><PERSON><PERSON> <saw<PERSON>@d1.dion.ne.jp>\n"
"Language-Team: German <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-Basepath: ../../FRHED\n"
"Language: ja\n"
"X-Generator: Poedit 2.0.7\n"

#. LANGUAGE, SUBLANGUAGE
#: heksedit.rc:5
#, c-format
msgid "LANG_ENGLISH, SUBLANG_ENGLISH_US"
msgstr "LANG_JAPANESE, SUBLANG_JAPANESE"

#. Codepage
#: heksedit.rc:6
#, c-format
msgid "1252"
msgstr "65001"

#: heksedit.rc:12
#, c-format
msgid "&File"
msgstr "ファイル(&F)"

#: heksedit.rc:14
#, c-format
msgid "&New\tCtrl+N"
msgstr "新規作成(&N)\tCtrl+N"

#: heksedit.rc:15
#, c-format
msgid "&Open...\tCtrl+O"
msgstr "開く(&O)...\tCtrl+O"

#: heksedit.rc:16
#, c-format
msgid "Open pa&rtially...\tAlt+P"
msgstr "部分的に開く(&R)...\tAlt+P"

#: heksedit.rc:18
#, c-format
msgid "&Save\tCtrl+S"
msgstr "保存(&S)\tCtrl+S"

#: heksedit.rc:19
#, c-format
msgid "Save &As...\tCtrl+J"
msgstr "名前を付けて保存(&A)...\tCtrl+J"

#: heksedit.rc:20
#, c-format
msgid "Save selec&tion as...\tCtrl+Shift+O"
msgstr "選択領域を保存(&T)...\tCtrl+Shift+O"

#: heksedit.rc:22
#, c-format
msgid "Re&vert\tCtrl+Shift+R"
msgstr "元に戻す(&V)\tCtrl+Shift+R"

#: heksedit.rc:23
#, c-format
msgid "&Insert file...\tCtrl+Shift+I"
msgstr "ファイルの内容を挿入(&I)...\tCtrl+Shift+I"

#: heksedit.rc:24
#, c-format
msgid "&Delete file...\tCtrl+Shift+D"
msgstr "ファイルを削除(&D)...\tCtrl+Shift+D"

#: heksedit.rc:25
#, c-format
msgid "Close File\tCtrl+Shift+X"
msgstr "ファイルを閉じる\tCtrl+Shift+X"

#: heksedit.rc:27
#, c-format
msgid "&Export as hexdump..."
msgstr "16 進ダンプ形式でエクスポート(&E)..."

#: heksedit.rc:28
#, c-format
msgid "I&mport from hexdump...\tCtrl+Shift+H"
msgstr "16 進ダンプ形式でインポート(&M)...\tCtrl+Shift+H"

#: heksedit.rc:40
#, c-format
msgid "E&xit\tAlt+F4"
msgstr "終了(&X)\tAlt+F4"

#: heksedit.rc:42
#, c-format
msgid "&Disk"
msgstr "ディスク(&D)"

#: heksedit.rc:44
#, c-format
msgid "&Open drive..."
msgstr "ドライブを開く(&O)..."

#: heksedit.rc:45
#, c-format
msgid "&Close drive"
msgstr "ドライブを閉じる(&C)"

#: heksedit.rc:47
#: heksedit.rc:190
#, c-format
msgid "Goto &First Sector"
msgstr "最初のセクタに移動(&F)"

#: heksedit.rc:48
#, c-format
msgid "Goto &Next Sector\tCtrl+Shift+PgDn"
msgstr "次のセクタに移動(&N)\tCtrl+Shift+PgDn"

#: heksedit.rc:49
#, c-format
msgid "Goto &Previous Sector\tCtrl+Shift+PgUp"
msgstr "前のセクタに移動(&P)\tCtrl+Shift+PgUp"

#: heksedit.rc:51
#: heksedit.rc:193
#, c-format
msgid "Goto &Last Sector"
msgstr "最後のセクタに移動(&L)"

#: heksedit.rc:53
#: heksedit.rc:195
#, c-format
msgid "Goto &Sector #..."
msgstr "指定したセクタに移動(&S)..."

#: heksedit.rc:55
#, c-format
msgid "&Edit"
msgstr "編集(&E)"

#: heksedit.rc:57
#, c-format
msgid "&Undo\tCtrl+Z"
msgstr "元に戻す(&U)\tCtrl+Z"

#: heksedit.rc:58
#, c-format
msgid "&Redo\tCtrl+Y"
msgstr "やり直し(&R)\tCtrl+Y"

#: heksedit.rc:60
#: heksedit.rc:177
#, c-format
msgid "Cu&t...\tCtrl+X"
msgstr "切り取り(&T)...\tCtrl+X"

#: heksedit.rc:61
#: heksedit.rc:178
#, c-format
msgid "&Copy...\tCtrl+C"
msgstr "コピー(&C)...\tCtrl+C"

#: heksedit.rc:62
#: heksedit.rc:179
#, c-format
msgid "&Paste...\tCtrl+V"
msgstr "貼り付け(&P)...\tCtrl+V"

#: heksedit.rc:63
#, c-format
msgid "Paste &with dialog...\tCtrl+Shift+V"
msgstr "内容を確認して貼り付け(&W)...\tCtrl+Shift+V"

#: heksedit.rc:64
#, c-format
msgid "&Move/Copy bytes...\tCtrl+Alt+M"
msgstr "データの移動/コピー(&M)...\tCtrl+Alt+M"

#: heksedit.rc:65
#, c-format
msgid "Reverse bytes...\tCtrl+Alt+R"
msgstr "データを逆順に並び替える...\tCtrl+Alt+R"

#: heksedit.rc:66
#, c-format
msgid "&Append...\tCtrl+P"
msgstr "追加(&A)...\tCtrl+P"

#: heksedit.rc:67
#: heksedit.rc:180
#, c-format
msgid "&Delete\tDel"
msgstr "削除(&D)\tDel"

#: heksedit.rc:68
#, c-format
msgid "&Select all\tCtrl+A"
msgstr "すべて選択(&S)\tCtrl+A"

#: heksedit.rc:69
#, c-format
msgid "Select &block...\tCtrl+E"
msgstr "選択範囲(&B)...\tCtrl+E"

#: heksedit.rc:70
#, c-format
msgid "&Fill selection...\tCtrl+Shift+F"
msgstr "選択領域を指定した値で埋める(&F)...\tCtrl+Shift+F"

#: heksedit.rc:72
#, c-format
msgid "Toggle h&ex/character editing\tTAB"
msgstr "16 進/文字編集の切り替え(&E)\tTAB"

#: heksedit.rc:73
#, c-format
msgid "Toggle keyboard I&nsert/Overwrite mode\tIns"
msgstr "挿入/上書きモードの切り替え(&N)\tIns"

#: heksedit.rc:75
#, c-format
msgid "Read-only mode\tAlt+R"
msgstr "読み取り専用モード\tAlt+R"

#: heksedit.rc:77
#, c-format
msgid "&Find and Replace"
msgstr "検索と置換(&F)"

#: heksedit.rc:79
#: heksedit.rc:182
#, c-format
msgid "&Find...\tCtrl+F"
msgstr "検索(&F)...\tCtrl+F"

#: heksedit.rc:80
#, c-format
msgid "&Replace...\tCtrl+H"
msgstr "置換(&R)...\tCtrl+H"

#: heksedit.rc:81
#, c-format
msgid "Find &next\tF3"
msgstr "次を検索(&N)\tF3"

#: heksedit.rc:82
#, c-format
msgid "Find &previous\tShift+F3"
msgstr "前を検索(&P)\tShift+F3"

#: heksedit.rc:84
#: heksedit.rc:186
#, c-format
msgid "&Go To...\tCtrl+G"
msgstr "移動(&G)...\tCtrl+G"

#: heksedit.rc:86
#, c-format
msgid "Enter decimal &value...\tCtrl+D"
msgstr "10 進数入力(&V)...\tCtrl+D"

#: heksedit.rc:88
#, c-format
msgid "Man&ipulate bits...\tCtrl+B"
msgstr "ビット操作(&I)...\tCtrl+B"

#: heksedit.rc:89
#, c-format
msgid "C&ompare from current offset...\tCtrl+M"
msgstr "現在のオフセットから比較(&O)...\tCtrl+M"

#: heksedit.rc:91
#, c-format
msgid "Get f&loating point value...\tCtrl+L"
msgstr "浮動小数点数の取得(&L)...\tCtrl+L"

#: heksedit.rc:92
#, c-format
msgid "File properties...\tAlt+Enter"
msgstr "ファイル プロパティ...\tAlt+Enter"

#: heksedit.rc:93
#, c-format
msgid "Appl&y template...\tAlt+T"
msgstr "テンプレートの適用(&Y)...\tAlt+T"

#: heksedit.rc:94
#, c-format
msgid "Open in text editor\tF5"
msgstr "テキスト エディタで開く\tF5"

#: heksedit.rc:96
#, c-format
msgid "&View"
msgstr "表示(&V)"

#: heksedit.rc:98
#, c-format
msgid "Scroll &left\tCtrl+Arrow Left"
msgstr "左へスクロール(&L)\tCtrl+Arrow Left"

#: heksedit.rc:99
#, c-format
msgid "Scroll &right\tCtrl+Arrow Right"
msgstr "右へスクロール(&R)\tCtrl+Arrow Right"

#: heksedit.rc:100
#, c-format
msgid "Scroll &up\tCtrl+Arrow Up"
msgstr "上へスクロール(&U)\tCtrl+Arrow Up"

#: heksedit.rc:101
#, c-format
msgid "Scroll &down\tCtrl+Arrow Down"
msgstr "下へスクロール(&D)\tCtrl+Arrow Down"

#: heksedit.rc:102
#, c-format
msgid "Scroll up a page\tCtrl+Page Up"
msgstr "1 ページ上へスクロール\tCtrl+Page Up"

#: heksedit.rc:103
#, c-format
msgid "Scroll down a page\tCtrl+Page Down"
msgstr "1 ページ下へスクロール\tCtrl+Page Down"

#: heksedit.rc:105
#, c-format
msgid "Zoom in\tCtrl++"
msgstr "拡大表示\tCtrl++"

#: heksedit.rc:106
#, c-format
msgid "Zoom out\tCtrl+-"
msgstr "縮小表示\tCtrl+-"

#: heksedit.rc:107
#, c-format
msgid "Zoom normal\tCtrl+*"
msgstr "通常表示\tCtrl+*"

#: heksedit.rc:109
#, c-format
msgid "&Options"
msgstr "設定(&O)"

#: heksedit.rc:111
#, c-format
msgid "&View settings...\tCtrl+I"
msgstr "表示設定(&V)...\tCtrl+I"

#: heksedit.rc:112
#, c-format
msgid "&Color settings"
msgstr "色設定(&C)"

#: heksedit.rc:114
#, c-format
msgid "&Text color...\tCtrl+T"
msgstr "文字色(&T)...\tCtrl+T"

#: heksedit.rc:115
#, c-format
msgid "&Background color...\tCtrl+K"
msgstr "背景色(&B)...\tCtrl+K"

#: heksedit.rc:116
#, c-format
msgid "Separator color...\tCtrl+Q"
msgstr "セパレータの色...\tCtrl+Q"

#: heksedit.rc:117
#, c-format
msgid "Selection text color...\tCtrl+Shift+T"
msgstr "選択文字の色...\tCtrl+Shift+T"

#: heksedit.rc:119
#, c-format
msgid "Selection background color...\tCtrl+Shift+K"
msgstr "選択領域の背景色...\tCtrl+Shift+K"

#: heksedit.rc:121
#, c-format
msgid "Bookmark color...\tCtrl+Shift+B"
msgstr "ブックマークの色...\tCtrl+Shift+B"

#: heksedit.rc:123
#, c-format
msgid "Reset colors to standard...\tCtrl+Shift+S"
msgstr "色設定を標準に戻す...\tCtrl+Shift+S"

#: heksedit.rc:125
#, c-format
msgid "&Adopt OS colour scheme...\tCtrl+Shift+A"
msgstr "OS のカラー スキーマを適用する(&A)...\tCtrl+Shift+A"

#: heksedit.rc:128
#, c-format
msgid "C&haracter set...\tCtrl+R"
msgstr "文字セット(&H)...\tCtrl+R"

#: heksedit.rc:129
#, c-format
msgid "B&inary mode...\tCtrl+Shift+Y"
msgstr "バイナリモード(&I)...\tCtrl+Shift+Y"

#: heksedit.rc:130
#, c-format
msgid "&OLE Drag-drop...\tCtrl+Alt+O"
msgstr "OLE ドラッグ&&ドロップ(&O)...\tCtrl+Alt+O"

#: heksedit.rc:131
#, c-format
msgid "Make backups\tCtrl+Shift+M"
msgstr "バックアップを作成する\tCtrl+Shift+M"

#: heksedit.rc:133
#, c-format
msgid "&Registry"
msgstr "レジストリ(&R)"

#: heksedit.rc:135
#, c-format
msgid "In &all context menu"
msgstr "すべてのコンテキストメニューに追加(&A)"

#: heksedit.rc:136
#, c-format
msgid "In &unknown context menu"
msgstr "未登録ファイル種別のコンテキストメニューに追加(&U)"

#: heksedit.rc:137
#, c-format
msgid "Is &default in unknown context"
msgstr "未登録ファイル種別のコンテキストメニューのデフォルトとする(&D)"

#: heksedit.rc:138
#, c-format
msgid "&Save ini when required"
msgstr "必要時に保存する(&S)"

#: heksedit.rc:139
#, c-format
msgid "Change &instance..."
msgstr "保存先/読込元インスタンス番号の変更(&I)..."

#: heksedit.rc:140
#, c-format
msgid "S&hortcuts to Frhed..."
msgstr "Frhed へのショートカット(&H)..."

#: heksedit.rc:142
#, c-format
msgid "&Bookmarks"
msgstr "ブックマーク(&B)"

#: heksedit.rc:144
#, c-format
msgid "&Add bookmark...\tCtrl+W"
msgstr "ブックマークを追加(&A)...\tCtrl+W"

#: heksedit.rc:145
#, c-format
msgid "&Remove bookmark...\tAlt+W"
msgstr "ブックマークを削除(&R)...\tAlt+W"

#: heksedit.rc:146
#, c-format
msgid "&Clear all bookmarks\tCtrl+Shift+W"
msgstr "すべてのブックマークをクリア(&C)\tCtrl+Shift+W"

#: heksedit.rc:158
#, c-format
msgid "&Misc"
msgstr "その他(&M)"

#: heksedit.rc:160
#, c-format
msgid "Goto DLL e&xport names..."
msgstr "DLL のエクスポート名格納位置に移動(&X)..."

#: heksedit.rc:161
#, c-format
msgid "Goto DLL import names..."
msgstr "DLL のインポート名格納位置に移動..."

#: heksedit.rc:163
#, c-format
msgid "Encode / Decode..."
msgstr "エンコード / デコード..."

#: heksedit.rc:165
#, c-format
msgid "&Help"
msgstr "ヘルプ(&H)"

#: heksedit.rc:167
#, c-format
msgid "&Help topics...\tF1"
msgstr "ヘルプ トピック(&H)...\tF1"

#: heksedit.rc:169
#, c-format
msgid "&About Frhed..."
msgstr "Frhed について(&A)..."

#: heksedit.rc:183
#, c-format
msgid "Replace...\tCtrl+H"
msgstr "置換...\tCtrl+H"

#: heksedit.rc:184
#, c-format
msgid "Find next\tF3"
msgstr "次を検索\tF3"

#: heksedit.rc:185
#, c-format
msgid "Find previous\tShift+F3"
msgstr "前を検索\tShift+F3"

#: heksedit.rc:191
#, c-format
msgid "Goto &Next Sector"
msgstr "次のセクタへ移動(&N)"

#: heksedit.rc:192
#, c-format
msgid "Goto &Previous Sector"
msgstr "前のセクタに移動(&P)"

#: heksedit.rc:298
#, c-format
msgid "Go to Offset"
msgstr "指定オフセットへの移動"

#: heksedit.rc:301
#, c-format
msgid "Start with \"+\" or \"-\" (minus) for relative jump."
msgstr "先頭に  \"+\" または \"-\" (マイナス) を入力すると相対移動"

#: heksedit.rc:303
#, c-format
msgid "Prefix offset with \"x\" to indicate hex notation."
msgstr "16 進数で指定したい場合、接頭辞として \"x\" を指定"

#: heksedit.rc:306
#: heksedit.rc:347
#: heksedit.rc:367
#: heksedit.rc:387
#: heksedit.rc:412
#: heksedit.rc:430
#: heksedit.rc:448
#: heksedit.rc:486
#: heksedit.rc:498
#: heksedit.rc:533
#: heksedit.rc:549
#: heksedit.rc:563
#: heksedit.rc:577
#: heksedit.rc:593
#: heksedit.rc:606
#: heksedit.rc:652
#: heksedit.rc:675
#: heksedit.rc:689
#: heksedit.rc:750
#: heksedit.rc:772
#: heksedit.rc:793
#: heksedit.rc:819
#: heksedit.rc:877
#: heksedit.rc:898
#: heksedit.rc:946
#: heksedit.rc:977
#, c-format
msgid "OK"
msgstr "OK"

#: heksedit.rc:307
#: heksedit.rc:325
#: heksedit.rc:368
#: heksedit.rc:388
#: heksedit.rc:413
#: heksedit.rc:431
#: heksedit.rc:449
#: heksedit.rc:487
#: heksedit.rc:499
#: heksedit.rc:534
#: heksedit.rc:550
#: heksedit.rc:564
#: heksedit.rc:578
#: heksedit.rc:594
#: heksedit.rc:607
#: heksedit.rc:622
#: heksedit.rc:634
#: heksedit.rc:653
#: heksedit.rc:676
#: heksedit.rc:690
#: heksedit.rc:716
#: heksedit.rc:751
#: heksedit.rc:773
#: heksedit.rc:794
#: heksedit.rc:806
#: heksedit.rc:820
#: heksedit.rc:878
#: heksedit.rc:899
#: heksedit.rc:947
#: heksedit.rc:960
#: heksedit.rc:978
#: heksedit.rc:1332
#, c-format
msgid "Cancel"
msgstr "キャンセル"

#: heksedit.rc:313
#: heksedit.rc:324
#: heksedit.rc:996
#, c-format
msgid "Find"
msgstr "検索"

#: heksedit.rc:316
#, c-format
msgid "&Find what:"
msgstr "検索する文字列(&F):"

#: heksedit.rc:318
#, c-format
msgid ""
"Please refer to the online help's Using the Special Syntax section for "
"searching for binary values."
msgstr ""
"バイナリ データの検索についてはオンライン ヘルプ「Using the Special Syntax」"
"セクションを参照してください。"

#: heksedit.rc:319
#, c-format
msgid "Direction"
msgstr "方向"

#: heksedit.rc:320
#, c-format
msgid "&Up"
msgstr "上へ(&U)"

#: heksedit.rc:321
#, c-format
msgid "&Down"
msgstr "下へ(&D)"

#: heksedit.rc:322
#, c-format
msgid "Match &case"
msgstr "大文字と小文字を区別する(&C)"

#: heksedit.rc:323
#, c-format
msgid "U&nicode"
msgstr "U&nicode"

#: heksedit.rc:331
#, c-format
msgid "About Frhed"
msgstr "Frhed について"

#: heksedit.rc:335
#, c-format
msgid "Frhed - Free hex editor"
msgstr "Frhed - Free hex editor"

#: heksedit.rc:338
#, c-format
msgid "(c) Raihan Kibria 2000"
msgstr "(c) Raihan Kibria 2000"

#: heksedit.rc:339
#, c-format
msgid "All rights reserved."
msgstr "All rights reserved."

#: heksedit.rc:340
#, c-format
msgid "Contributors are listed in Contributors.txt."
msgstr "貢献者は Contributors.txt にリストされています。"

#: heksedit.rc:342
#, c-format
msgid "Open contributors list"
msgstr "貢献者リストを開く"

#: heksedit.rc:343
#, c-format
msgid "Homepage"
msgstr "ホームページ"

#: heksedit.rc:345
#, c-format
msgid "Open Frhed's homepage in browser"
msgstr "Frhed のホームページをブラウザで開く"

#: heksedit.rc:353
#, c-format
msgid "Export Hexdump"
msgstr "16 進ダンプ形式でエクスポート"

#: heksedit.rc:356
#, c-format
msgid "Start at offset:"
msgstr "開始オフセット:"

#: heksedit.rc:358
#, c-format
msgid "End at offset:"
msgstr "終了オフセット:"

#: heksedit.rc:360
#, c-format
msgid "Export to"
msgstr "エクスポート先"

#: heksedit.rc:361
#, c-format
msgid "File"
msgstr "ファイル"

#: heksedit.rc:362
#, c-format
msgid "Clipboard"
msgstr "クリップボード"

#: heksedit.rc:363
#, c-format
msgid "Type"
msgstr "種類"

#: heksedit.rc:364
#, c-format
msgid "Frhed display as text"
msgstr "Frhed の表示形式でテキスト化"

#: heksedit.rc:365
#, c-format
msgid "Just hex digits on a line"
msgstr "16 進数データの羅列(改行なし)"

#: heksedit.rc:366
#, c-format
msgid "Frhed display as RTF"
msgstr "Frhed の表示形式でRTF化"

#: heksedit.rc:374
#, c-format
msgid "Enter decimal value"
msgstr "10 進数データの入力"

#: heksedit.rc:377
#, c-format
msgid "Enter decimal value:"
msgstr "10 進数データの入力:"

#: heksedit.rc:379
#, c-format
msgid "At offset:"
msgstr "オフセット:"

#: heksedit.rc:381
#, c-format
msgid "Number of times:"
msgstr "繰り返し回数:"

#: heksedit.rc:383
#, c-format
msgid "Size of Value:"
msgstr "値のサイズ:"

#: heksedit.rc:384
#, c-format
msgid "Byte (8 bit)"
msgstr "Byte (8 Bit)"

#: heksedit.rc:385
#, c-format
msgid "Word (16 bit)"
msgstr "Word (16 Bit)"

#: heksedit.rc:386
#, c-format
msgid "Double word (32 bit)"
msgstr "Double word (32 bit)"

#: heksedit.rc:394
#, c-format
msgid "Paste with dialogue"
msgstr "内容を確認して貼り付け"

#: heksedit.rc:397
#, c-format
msgid "Clipboard content (text):"
msgstr "クリップボードの内容 (テキスト):"

#: heksedit.rc:400
#: heksedit.rc:662
#, c-format
msgid "Paste mode"
msgstr "貼り付けモード"

#: heksedit.rc:401
#: heksedit.rc:663
#, c-format
msgid "Overwrite"
msgstr "上書き"

#: heksedit.rc:403
#: heksedit.rc:664
#, c-format
msgid "Insert"
msgstr "挿入"

#: heksedit.rc:405
#: heksedit.rc:665
#, c-format
msgid "Paste data how many times:"
msgstr "データ貼り付け回数:"

#: heksedit.rc:407
#: heksedit.rc:669
#, c-format
msgid "Paste coded binary values as text"
msgstr "<bh:..>等の記法を無視して貼付け"

#: heksedit.rc:409
#, c-format
msgid "Skip how many bytes between inserts/overwrites"
msgstr "貼り付けの繰り返し毎にスキップするバイト数"

#: heksedit.rc:419
#: heksedit.rc:1000
#, c-format
msgid "Cut"
msgstr "切り取り"

#: heksedit.rc:422
#, c-format
msgid "Start cutting at offset:"
msgstr "切り取り開始オフセット:"

#: heksedit.rc:424
#, c-format
msgid "Cut how many bytes"
msgstr "切り取り方法"

#: heksedit.rc:425
#, c-format
msgid "Cut up to and including offset:"
msgstr "指定オフセットまで含めて切り取り:"

#: heksedit.rc:427
#, c-format
msgid "Number of bytes to cut:"
msgstr "バイト数指定で切り取り:"

#: heksedit.rc:437
#: heksedit.rc:865
#: heksedit.rc:896
#: heksedit.rc:997
#: heksedit.rc:1331
#, c-format
msgid "Copy"
msgstr "コピー"

#: heksedit.rc:440
#, c-format
msgid "Start copying at offset:"
msgstr "コピー開始オフセット:"

#: heksedit.rc:442
#, c-format
msgid "Copy how many bytes"
msgstr "コピー方法"

#: heksedit.rc:443
#, c-format
msgid "Copy up to and including offset:"
msgstr "指定オフセットまで含めてコピー:"

#: heksedit.rc:445
#, c-format
msgid "Number of bytes to copy:"
msgstr "バイト数指定でコピー:"

#: heksedit.rc:455
#, c-format
msgid "View Settings"
msgstr "表示設定"

#: heksedit.rc:458
#, c-format
msgid "Number of bytes to display per line:"
msgstr "1 行あたりの表示バイト数:"

#: heksedit.rc:461
#, c-format
msgid ""
"Automatically adjust number of bytes per line (uncheck this if you want "
"Frhed to use your own choice for bytes per line)"
msgstr ""
"自動的に 1 行あたりのバイト数を調整する\n"
"(1 行あたりのバイト数を指定したい場合、チェックを外してください)"

#: heksedit.rc:464
#, c-format
msgid "Display length of offset in how many characters:"
msgstr "オフセットの表示桁数:"

#: heksedit.rc:467
#, c-format
msgid "Adjust offset length to that of the max offset"
msgstr "最大オフセットに合わせて表示桁数を調整する"

#: heksedit.rc:470
#, c-format
msgid "Display values at caret position as:"
msgstr "カレット位置の値の表示:"

#: heksedit.rc:472
#, c-format
msgid "unsigned"
msgstr "unsigned"

#: heksedit.rc:474
#, c-format
msgid "signed"
msgstr "signed"

#: heksedit.rc:476
#, c-format
msgid "Set read-only mode on opening files"
msgstr "ファイル オープン後読取専用にする"

#: heksedit.rc:479
#, c-format
msgid "Path and filename of the text editor to call:"
msgstr "テキスト エディタのパス:"

#: heksedit.rc:482
#, c-format
msgid "Browse..."
msgstr "参照..."

#: heksedit.rc:483
#, c-format
msgid "Language:"
msgstr "言語:"

#: heksedit.rc:493
#, c-format
msgid "Append"
msgstr "追加"

#: heksedit.rc:496
#, c-format
msgid "Bytes to append to the end of the file:"
msgstr "ファイルの最後に追加するバイト数:"

#: heksedit.rc:504
#, c-format
msgid "Manipulate Bits"
msgstr "ビット操作"

#: heksedit.rc:508
#: heksedit.rc:528
#, c-format
msgid "8"
msgstr "8"

#: heksedit.rc:509
#, c-format
msgid "7"
msgstr "7"

#: heksedit.rc:510
#, c-format
msgid "6"
msgstr "6"

#: heksedit.rc:511
#, c-format
msgid "5"
msgstr "5"

#: heksedit.rc:512
#: heksedit.rc:529
#, c-format
msgid "4"
msgstr "4"

#: heksedit.rc:513
#, c-format
msgid "3"
msgstr "3"

#: heksedit.rc:514
#: heksedit.rc:530
#, c-format
msgid "2"
msgstr "2"

#: heksedit.rc:524
#, c-format
msgid "128"
msgstr "128"

#: heksedit.rc:525
#, c-format
msgid "64"
msgstr "64"

#: heksedit.rc:526
#, c-format
msgid "32"
msgstr "32"

#: heksedit.rc:527
#, c-format
msgid "16"
msgstr "16"

#: heksedit.rc:540
#, c-format
msgid "Character Set"
msgstr "文字セット"

#: heksedit.rc:543
#, c-format
msgid "Choose character set"
msgstr "文字セットの選択"

#: heksedit.rc:544
#: heksedit.rc:1024
#, c-format
msgid "ANSI"
msgstr "ANSI"

#: heksedit.rc:546
#: heksedit.rc:1025
#, c-format
msgid "OEM"
msgstr "OEM"

#: heksedit.rc:547
#, c-format
msgid "Font size in points:"
msgstr "フォント サイズ (pt):"

#: heksedit.rc:556
#, c-format
msgid "Choose area of difference to display"
msgstr "表示する差異箇所の選択"

#: heksedit.rc:559
#, c-format
msgid "file_sizes"
msgstr "file_sizes"

#: heksedit.rc:560
#, c-format
msgid "number_of_diffs"
msgstr "number_of_diffs"

#: heksedit.rc:565
#, c-format
msgid "Copy above list"
msgstr "上記をコピー"

#: heksedit.rc:571
#, c-format
msgid "Binary Mode"
msgstr "バイナリモード"

#: heksedit.rc:574
#, c-format
msgid "Choose binary mode"
msgstr "バイナリモードの選択"

#: heksedit.rc:575
#, c-format
msgid "Little-endian (Intel)"
msgstr "リトルエンディアン (Intel)"

#: heksedit.rc:576
#, c-format
msgid "Big-endian (Motorola)"
msgstr "ビッグエンディアン (Motorola)"

#: heksedit.rc:584
#, c-format
msgid "Select Block"
msgstr "選択範囲"

#: heksedit.rc:587
#, c-format
msgid "Start of selection (prefix x for hex):"
msgstr "選択範囲の開始 (接頭辞 x は 16 進数を表す):"

#: heksedit.rc:590
#, c-format
msgid "End of selection (prefix x for hex):"
msgstr "選択範囲の終了 (接頭辞 x は 16 進数を表す):"

#: heksedit.rc:599
#, c-format
msgid "Reverse Bytes"
msgstr "データの逆順並び替え"

#: heksedit.rc:602
#, c-format
msgid "Offset of first byte:"
msgstr "先頭バイトのオフセット:"

#: heksedit.rc:604
#, c-format
msgid "Offset of last byte:"
msgstr "最終バイトのオフセット:"

#: heksedit.rc:613
#, c-format
msgid "Add Bookmark"
msgstr "ブックマークの追加"

#: heksedit.rc:616
#, c-format
msgid "Offset (prefix x for hex):"
msgstr "オフセット (接頭辞 x は 16 進数を表す ):"

#: heksedit.rc:619
#, c-format
msgid "Name:"
msgstr "名前:"

#: heksedit.rc:621
#, c-format
msgid "Add"
msgstr "追加"

#: heksedit.rc:628
#, c-format
msgid "Remove Bookmark"
msgstr "ブックマークの削除"

#: heksedit.rc:633
#, c-format
msgid "Remove"
msgstr "削除"

#: heksedit.rc:640
#, c-format
msgid "Open partially"
msgstr "部分オープン"

#: heksedit.rc:643
#, c-format
msgid "Start opening partially"
msgstr "部分読み込みの開始位置"

#: heksedit.rc:644
#, c-format
msgid "&At offset from the beginning of file (prefix x for hex):"
msgstr "ファイルの先頭からのオフセット(&A) (接頭辞 x は 16 進数を表す):"

#: heksedit.rc:646
#, c-format
msgid "&From end of file"
msgstr "ファイルの終端から(&F)"

#: heksedit.rc:648
#, c-format
msgid "&How many bytes:"
msgstr "バイト数(&H):"

#: heksedit.rc:650
#, c-format
msgid "&Display offsets relative to start of file (or start of loaded block)"
msgstr "ファイル (またはブロック) の先頭からの相対オフセットを表示する(&D)"

#: heksedit.rc:659
#: heksedit.rc:998
#, c-format
msgid "Paste"
msgstr "貼り付け"

#: heksedit.rc:667
#, c-format
msgid "Bytes to skip between inserts/overwrites:"
msgstr "貼り付けの繰り返し毎にスキップするバイト数:"

#: heksedit.rc:671
#, c-format
msgid "Clipboard format to use:"
msgstr "使用するクリップボードの形式:"

#: heksedit.rc:673
#, c-format
msgid "Refresh"
msgstr "再読込"

#: heksedit.rc:674
#, c-format
msgid "- CF_TEXT was used by previous versions of Frhed\n- Those starting w CF_ are standard formats && others are registered/private ones\n- Some formats may have been synthesized from the original."
msgstr ""
"- CF_TEXT は Frhed の前のバージョンで使用されていました\n"
"- CF_ で始まるものは標準フォーマット、その他は後で登録されたもの/内部用\n"
"- いくつかのフォーマットはオリジナルを元に合成されているかもしれません"

#: heksedit.rc:682
#, c-format
msgid "Template"
msgstr "テンプレート"

#: heksedit.rc:685
#, c-format
msgid "Result of template application:"
msgstr "テンプレート適用結果:"

#: heksedit.rc:696
#: heksedit.rc:715
#: heksedit.rc:1005
#, c-format
msgid "Replace"
msgstr "置換"

#: heksedit.rc:699
#, c-format
msgid "Find what:"
msgstr "検索する文字列:"

#: heksedit.rc:702
#, c-format
msgid "Find next"
msgstr "次を検索"

#: heksedit.rc:703
#, c-format
msgid "Find previous"
msgstr "前を検索"

#: heksedit.rc:704
#, c-format
msgid "Match case"
msgstr "大文字と小文字を区別"

#: heksedit.rc:706
#, c-format
msgid "Replace with:"
msgstr "置換後の文字列:"

#: heksedit.rc:708
#, c-format
msgid "Replace all..."
msgstr "すべて置換..."

#: heksedit.rc:709
#, c-format
msgid "... following occurrences"
msgstr "... 次の出現箇所から"

#: heksedit.rc:711
#, c-format
msgid "... previous occurrences"
msgstr "... 前の出現箇所から"

#: heksedit.rc:713
#, c-format
msgid "Use special syntax"
msgstr "特殊記法を使用"

#: heksedit.rc:721
#, c-format
msgid "Fill Selection"
msgstr "選択範囲を指定データで埋める"

#: heksedit.rc:724
#, c-format
msgid "Fill from:"
msgstr "入力元:"

#: heksedit.rc:726
#: heksedit.rc:1298
#, c-format
msgid "File:"
msgstr "ファイル:"

#: heksedit.rc:728
#, c-format
msgid "&Browse..."
msgstr "参照(&B)..."

#: heksedit.rc:729
#, c-format
msgid "Hex:"
msgstr "16 進数:"

#: heksedit.rc:731
#, c-format
msgid "Selection"
msgstr "選択範囲"

#: heksedit.rc:732
#, c-format
msgid "Start:"
msgstr "開始:"

#: heksedit.rc:734
#, c-format
msgid "End:"
msgstr "終了:"

#: heksedit.rc:736
#, c-format
msgid "Size:"
msgstr "サイズ:"

#: heksedit.rc:738
#: heksedit.rc:920
#, c-format
msgid "Input"
msgstr "入力"

#: heksedit.rc:739
#, c-format
msgid "Size of input:"
msgstr "入力のサイズ:"

#: heksedit.rc:741
#, c-format
msgid "# Times input fits:"
msgstr "フィットする入力回数:"

#: heksedit.rc:743
#, c-format
msgid "Remainder:"
msgstr "余り:"

#: heksedit.rc:745
#, c-format
msgid "Assignment operator"
msgstr "代入演算子"

#: heksedit.rc:746
#, c-format
msgid "Assign (=)"
msgstr "代入 (=)"

#: heksedit.rc:747
#, c-format
msgid "OR (|=)"
msgstr "OR (|=)"

#: heksedit.rc:748
#, c-format
msgid "AND (&&=)"
msgstr "AND (&&=)"

#: heksedit.rc:749
#, c-format
msgid "XOR (^=)"
msgstr "XOR (^=)"

#: heksedit.rc:757
#, c-format
msgid "Change Instance"
msgstr "保存先/読込元インスタンス番号の変更"

#: heksedit.rc:760
#, c-format
msgid "Load the following instance data:"
msgstr "読み込むインスタンス データの番号:"

#: heksedit.rc:766
#, c-format
msgid "and save (when quiting) to this instance data:"
msgstr "インスタンス データの (終了時) 保存先番号:"

#: heksedit.rc:774
#, c-format
msgid ""
"Remember that if any instance of Frhed is running on the same instance data "
"as this one it will overwrite the data of this one if it exits last.  "
"Unless, that is, that the option to save ini data to the registry on exit is "
"disabled)"
msgstr ""
"もしある Frhed のインスタンスが別のインスタンスと同じインスタンス データを参"
"照している場合、最後に終了したもののデータで上書きされることに注意してくださ"
"い。(「必要時に保存する」を無効していない限り)"

#: heksedit.rc:781
#, c-format
msgid "Encode / Decode Data"
msgstr "データのエンコード/デコード"

#: heksedit.rc:784
#, c-format
msgid "Algorithms:"
msgstr "アルゴリズム:"

#: heksedit.rc:787
#, c-format
msgid "Arguments:"
msgstr "引数:"

#: heksedit.rc:789
#, c-format
msgid "Encode"
msgstr "エンコード"

#: heksedit.rc:791
#, c-format
msgid "Decode"
msgstr "デコード"

#: heksedit.rc:800
#, c-format
msgid "Open Drive"
msgstr "ドライブを開く"

#: heksedit.rc:804
#, c-format
msgid ""
"NOTE: Frhed allows only reading the drive data. Changing and saving the "
"drive data is not possible."
msgstr ""
"注意: Frhed はドライブ データの読み取りのみ可能です。ドライブ データの変更や"
"保存を行うことはできません。"

#: heksedit.rc:805
#: heksedit.rc:959
#: heksedit.rc:989
#, c-format
msgid "Open"
msgstr "開く"

#: heksedit.rc:812
#, c-format
msgid "Go to Track"
msgstr "指定セクタへの移動"

#: heksedit.rc:815
#, c-format
msgid "Drive Information:"
msgstr "ドライブ情報:"

#: heksedit.rc:817
#, c-format
msgid "&Go to track:"
msgstr "移動するセクタ(&G):"

#: heksedit.rc:826
#, c-format
msgid "Shortcuts"
msgstr "ショートカット"

#: heksedit.rc:829
#, c-format
msgid "Paths of shortcuts to Frhed:"
msgstr "Frhed へのショートカットのパス:"

#: heksedit.rc:830
#, c-format
msgid "Links to Frhed"
msgstr "Frhed へのリンク"

#: heksedit.rc:833
#, c-format
msgid "&Find links to any Frhed.exe, fix them and list them here."
msgstr "Frhed.exe へのリンクを検索し、リンクの更新を行う。(&F)"

#: heksedit.rc:836
#, c-format
msgid "Create link in/on:"
msgstr "リンクの作成:"

#: heksedit.rc:837
#, c-format
msgid "&Desktop"
msgstr "ﾃﾞｽｸﾄｯﾌﾟ"

#: heksedit.rc:839
#, c-format
msgid "S&tart Menu"
msgstr "ｽﾀｰﾄﾒﾆｭｰ"

#: heksedit.rc:841
#, c-format
msgid "&Send To"
msgstr "送る(&S)"

#: heksedit.rc:843
#, c-format
msgid "&Programs"
msgstr "プログラム(&P)"

#: heksedit.rc:845
#, c-format
msgid "Link management:"
msgstr "リンク管理:"

#: heksedit.rc:846
#, c-format
msgid "<< &Add..."
msgstr "<< 追加(&A)"

#: heksedit.rc:847
#, c-format
msgid "&Update list"
msgstr "一覧更新(&U)"

#: heksedit.rc:849
#, c-format
msgid ">> &Delete"
msgstr ">> 削除(&D)"

#: heksedit.rc:850
#, c-format
msgid "&Move"
msgstr "移動(&M)"

#: heksedit.rc:852
#, c-format
msgid "&Reload"
msgstr "再読込(&R)"

#: heksedit.rc:853
#, c-format
msgid "&Close"
msgstr "閉じる(&C)"

#: heksedit.rc:859
#, c-format
msgid "Move/Copy bytes"
msgstr "データの移動/コピー"

#: heksedit.rc:862
#, c-format
msgid "Operation:"
msgstr "操作:"

#: heksedit.rc:863
#, c-format
msgid ""
"Start with (-) for negative length or movement backwards. Prefix with x for "
"hex notation."
msgstr ""
"(-)を前に付加した場合、負の長さまたは後方への移動を表す。接頭辞 x は 16 進数"
"を表す。"

#: heksedit.rc:864
#: heksedit.rc:894
#: heksedit.rc:1330
#, c-format
msgid "Move"
msgstr "移動"

#: heksedit.rc:866
#, c-format
msgid "Begin of the block:"
msgstr "ブロックの開始位置:"

#: heksedit.rc:868
#, c-format
msgid "End of the block:"
msgstr "ブロックの終了位置:"

#: heksedit.rc:870
#, c-format
msgid "Offset"
msgstr "オフセット"

#: heksedit.rc:871
#, c-format
msgid "Length"
msgstr "長さ"

#: heksedit.rc:872
#, c-format
msgid "Target:"
msgstr "移動/コピー先:"

#: heksedit.rc:874
#, c-format
msgid "Bytes forward"
msgstr "前方への相対バイト数"

#: heksedit.rc:875
#, c-format
msgid "First byte offset"
msgstr "先頭バイトからのオフセット"

#: heksedit.rc:876
#, c-format
msgid "Last byte offset"
msgstr "最終バイトからのオフセット"

#: heksedit.rc:884
#, c-format
msgid "Drag-drop"
msgstr "ドラッグ&&ドロップ"

#: heksedit.rc:887
#, c-format
msgid "Clipboard format[s] to use:"
msgstr "使用するクリップボードの形式:"

#: heksedit.rc:891
#, c-format
msgid "Up"
msgstr "上へ"

#: heksedit.rc:892
#, c-format
msgid "Down"
msgstr "下へ"

#: heksedit.rc:893
#, c-format
msgid "Data:"
msgstr "データ:"

#: heksedit.rc:905
#, c-format
msgid "OLE Drag-drop options"
msgstr "OLE ドラッグ&&ドロップ オプション"

#: heksedit.rc:908
#, c-format
msgid "General"
msgstr "全般"

#: heksedit.rc:909
#, c-format
msgid "Enable drop input"
msgstr "ドロップ入力を有効にする"

#: heksedit.rc:911
#, c-format
msgid "Enable drag output"
msgstr "ドラッグ出力を有効にする"

#: heksedit.rc:913
#, c-format
msgid "Enable scroll-delaying for drag-drop"
msgstr "ﾄﾞﾗｯｸﾞ&&ﾄﾞﾛｯﾌﾟ用にスクロール遅延を有効"

#: heksedit.rc:915
#, c-format
msgid "Enable scroll-delaying for selecting"
msgstr "選択用にスクロール遅延を有効にする"

#: heksedit.rc:917
#, c-format
msgid ""
"Always give the opportunity to change between move and copy after a drop."
msgstr "ドロップ後毎度移動するかコピーするかを選ばせる。"

#: heksedit.rc:921
#, c-format
msgid "CF_HDROP opens the files dropped."
msgstr "CF_HDROP でドロップされたファイルを開く"

#: heksedit.rc:923
#, c-format
msgid "Drop \"BinaryData\" if present"
msgstr "存在すれば \"BinaryData\" をドロップする"

#: heksedit.rc:926
#, c-format
msgid "Drop CF_TEXT if present"
msgstr "存在すれば CF_TEXT をドロップする"

#: heksedit.rc:929
#, c-format
msgid "Output"
msgstr "出力"

#: heksedit.rc:930
#, c-format
msgid "Drag \"BinaryData\""
msgstr "\"BinaryData\" をドラッグする"

#: heksedit.rc:933
#, c-format
msgid "Drag CF_TEXT as:"
msgstr "CF_TEXT を以下のものとしてドラッグする:"

#: heksedit.rc:936
#, c-format
msgid "Hexdump"
msgstr "16 進ダンプ"

#: heksedit.rc:938
#, c-format
msgid "Special syntax (<bh:ff>...)"
msgstr "特殊記法 (<bh:ff>...)"

#: heksedit.rc:940
#, c-format
msgid "display (else digits)"
msgstr "表示内容のまま"

#: heksedit.rc:943
#, c-format
msgid "Drag \"Rich Text Format\" (as hexdump)"
msgstr "\"Rich Text Format\" でドラッグする (16 進ダンプ)"

#: heksedit.rc:953
#, c-format
msgid "Multiple files dropped"
msgstr "複数のファイルがドロップされました"

#: heksedit.rc:956
#, c-format
msgid "Select the file to open:"
msgstr "開くファイルの選択:"

#: heksedit.rc:966
#, c-format
msgid "Delete"
msgstr "削除"

#: heksedit.rc:969
#, c-format
msgid "Start deleting at offset:"
msgstr "削除開始オフセット:"

#: heksedit.rc:971
#, c-format
msgid "Delete how many bytes"
msgstr "削除方法"

#: heksedit.rc:972
#, c-format
msgid "Delete up to and including offset:"
msgstr "指定オフセットまで含めて削除:"

#: heksedit.rc:974
#, c-format
msgid "Number of bytes to delete:"
msgstr "バイト数指定で削除:"

#: heksedit.rc:990
#, c-format
msgid "New"
msgstr "新規作成"

#: heksedit.rc:995
#, c-format
msgid "Save"
msgstr "保存"

#: heksedit.rc:999
#, c-format
msgid "Help"
msgstr "ヘルプ"

#: heksedit.rc:1010
#, c-format
msgid "Goto next sector"
msgstr "次のセクタへ移動"

#: heksedit.rc:1011
#, c-format
msgid "Goto previous sector"
msgstr "前のセクタへ移動"

#: heksedit.rc:1012
#, c-format
msgid "Goto first sector"
msgstr "最初のセクタへ移動"

#: heksedit.rc:1013
#, c-format
msgid "Goto last sector"
msgstr "最後のセクタへ移動"

#: heksedit.rc:1019
#, c-format
msgid "Frhed"
msgstr "Frhed"

#: heksedit.rc:1020
#, c-format
msgid "Version %u.%u.%u"
msgstr "Version %u.%u.%u"

#: heksedit.rc:1021
#, c-format
msgid "%d) 0x%x=%u to 0x%x=%u (%d bytes)"
msgstr "%d) 0x%x=%u to 0x%x=%u (%d バイト)"

#: heksedit.rc:1022
#, c-format
msgid "All Files (*.*)|*.*|"
msgstr "すべてのファイル (*.*)|*.*|"

#: heksedit.rc:1023
#, c-format
msgid "Untitled"
msgstr "無題"

#: heksedit.rc:1026
#, c-format
msgid "READ"
msgstr "読専"

#: heksedit.rc:1027
#, c-format
msgid "OVR"
msgstr "上書"

#: heksedit.rc:1028
#, c-format
msgid "INS"
msgstr "挿入"

#: heksedit.rc:1029
#: heksedit.rc:1290
#, c-format
msgid "L"
msgstr "L"

#: heksedit.rc:1030
#: heksedit.rc:1287
#, c-format
msgid "B"
msgstr "B"

#: heksedit.rc:1031
#, c-format
msgid ""
"You are trying to open a link file.\n"
"Click on Yes if you want to open the file linked to,\n"
"or click on No if you want to open the link file itself.\n"
"Choose Cancel if you want to abort opening."
msgstr ""
"リンク ファイルを開こうとしています。\n"
"リンク先のファイルを開きたい場合は [はい] を\n"
"リンク ファイル自体を開きたい場合は [いいえ] をクリックしてください。\n"
"どちらも開きたくない場合は [キャンセル] を選択してください。"

#: heksedit.rc:1037
#, c-format
msgid "Start offset not recognized."
msgstr "開始オフセットが認識できません。"

#: heksedit.rc:1038
#, c-format
msgid "End offset not recognized."
msgstr "終了オフセットが認識できません。"

#: heksedit.rc:1039
#, c-format
msgid "Offset not recognized."
msgstr "オフセットが認識できません。"

#: heksedit.rc:1045
#, c-format
msgid "Error while reading from file."
msgstr "ファイル読み取り中にエラーが発生ました。"

#: heksedit.rc:1046
#, c-format
msgid "Not enough memory."
msgstr "十分なメモリがありません。"

#: heksedit.rc:1047
#, c-format
msgid "Number of bytes not recognized."
msgstr "バイト数が認識できません。"

#: heksedit.rc:1048
#, c-format
msgid "Error opening file."
msgstr "ファイルオープン時にエラーが発生しました。"

#: heksedit.rc:1049
#, c-format
msgid "Not enough memory to load file."
msgstr "ファイルをロードするのに十分なメモリがありません"

#: heksedit.rc:1050
#, c-format
msgid "Could not save the file."
msgstr "ファイルを保存することができませんでした。"

#: heksedit.rc:1051
#, c-format
msgid ""
"Could not backup file!\n"
"Backup aborted, Save continuing."
msgstr ""
"ファイルのバックアップをすることができませんでした!\n"
"バックアップを中止しました。保存を継続します。"

#: heksedit.rc:1052
#, c-format
msgid "Could not save partially opened file."
msgstr "部分的に開いたファイルを保存することができませんでした。"

#: heksedit.rc:1053
#, c-format
msgid "Could not move data in the file."
msgstr "ファイル内のデータを移動することができませんでした。"

#: heksedit.rc:1054
#, c-format
msgid "Could not resize the file."
msgstr "ファイルをリサイズすることができませんでした。"

#: heksedit.rc:1055
#, c-format
msgid "Could not seek in file."
msgstr "ファイルをシークすることができませんでした。"

#: heksedit.rc:1056
#, c-format
msgid "Could not write data to file."
msgstr "ファイルへデータを書き込むことができませんでした。"

#: heksedit.rc:1057
#, c-format
msgid ""
"Help file\n"
"%s\n"
"not found!"
msgstr ""
"ヘルプ ファイル\n"
"%s\n"
"が見つかりません!"

#: heksedit.rc:1058
#, c-format
msgid "Error code 0x%x occurred while opening file %s."
msgstr "エラー コード 0x%x が発生しました。(ファイル %s)"

#: heksedit.rc:1059
#, c-format
msgid "Could not save preferences to registry."
msgstr "設定情報をレジストリに保存することができませんでした。"

#: heksedit.rc:1060
#, c-format
msgid "File is empty."
msgstr "ファイルは空です。"

#: heksedit.rc:1061
#, c-format
msgid "An error occurred when calling the text editor."
msgstr "テキスト エディタ起動時にエラーが発生しました。"

#: heksedit.rc:1062
#, c-format
msgid "Could not insert data."
msgstr "データを挿入することができませんでした。"

#: heksedit.rc:1063
#, c-format
msgid "Error checking file size"
msgstr "ファイル サイズ チェック エラー"

#: heksedit.rc:1064
#, c-format
msgid "No data present. Cannot continue!"
msgstr "データがありません。継続できません!"

#: heksedit.rc:1065
#, c-format
msgid ""
"Could not get text from the file.\n"
"Cannot continue!"
msgstr ""
"ファイルからテキストを取得することができませんでした。\n"
"継続できません!"

#: heksedit.rc:1071
#, c-format
msgid "KB"
msgstr "KB"

#: heksedit.rc:1072
#, c-format
msgid "MB"
msgstr "MB"

#: heksedit.rc:1073
#, c-format
msgid "GB"
msgstr "GB"

#: heksedit.rc:1074
#, c-format
msgid "TB"
msgstr "TB"

#: heksedit.rc:1080
#, c-format
msgid "Could not open the browser."
msgstr "ブラウザで開くことができませんでした。"

#: heksedit.rc:1081
#, c-format
msgid ""
"File\n"
"%s\n"
"not found!"
msgstr ""
"ファイル\n"
"%s\n"
"は見つかりません!"

#: heksedit.rc:1087
#, c-format
msgid "Can not set bookmark at that position."
msgstr "その場所にはブックマークを設置できません。"

#: heksedit.rc:1088
#, c-format
msgid "There already is a bookmark on that position."
msgstr "その場所にはすでにブックマークが設置されています。"

#: heksedit.rc:1089
#, c-format
msgid "Can not set bookmark in empty file."
msgstr "空のファイルにブックマークを設置することはできません。"

#: heksedit.rc:1090
#, c-format
msgid "Can not set any more bookmarks."
msgstr "これ以上ブックマークを設置できません。"

#: heksedit.rc:1091
#, c-format
msgid "Bookmark points to invalid position."
msgstr "ブックマークは不正な位置を示しています。"

#: heksedit.rc:1092
#, c-format
msgid "No bookmarks to remove."
msgstr "削除するブックマークが指定されていません。"

#: heksedit.rc:1093
#, c-format
msgid "Remove all bookmarks?"
msgstr "すべてのブックマークを削除しますか?"

#: heksedit.rc:1099
#, c-format
msgid "Number of bytes to append not recognized."
msgstr "追加するバイト数が認識できません。"

#: heksedit.rc:1100
#, c-format
msgid "Not enough memory for appending."
msgstr "追加するのに十分なメモリがありません。"

#: heksedit.rc:1106
#, c-format
msgid "Offset: 0x%x = %d"
msgstr "オフセット: 0x%x = %d"

#: heksedit.rc:1107
#, c-format
msgid "Value: 0x%02x, %d signed, %u unsigned."
msgstr "値: 0x%02x, %d signed, %u unsigned"

#: heksedit.rc:1113
#, c-format
msgid "Choose file to compare with"
msgstr "比較するファイルの選択"

#: heksedit.rc:1114
#, c-format
msgid "Error while opening file."
msgstr "ファイルのオープンでエラーが発生しました。"

#: heksedit.rc:1115
#, c-format
msgid "%d areas of difference found."
msgstr "%d 箇所差異があります。"

#: heksedit.rc:1116
#, c-format
msgid "Remaining loaded data size: %d, size of file on disk: %d."
msgstr "読み込まれたデータ サイズ: %d, ディスク上のファイル サイズ: %d"

#: heksedit.rc:1117
#, c-format
msgid "Data matches exactly."
msgstr "データはすべて一致しています。"

#: heksedit.rc:1123
#, c-format
msgid "Cannot get access to clipboard."
msgstr "クリップボードにアクセスできません。"

#: heksedit.rc:1124
#, c-format
msgid "Cannot lock clipboard."
msgstr "クリップボードをロックできません。"

#: heksedit.rc:1125
#: heksedit.rc:1136
#, c-format
msgid "Not enough memory for copying."
msgstr "コピーするのに十分なメモリがありません。"

#: heksedit.rc:1126
#, c-format
msgid ""
"\n"
"Do you want the above output to be copied to the clipboard?\n"
msgstr ""
"\n"
"上記の値をクリップボードにコピーしますか?\n"

#: heksedit.rc:1127
#, c-format
msgid "You need to select a clipboard format to use."
msgstr "使用するクリップボード フォーマットを選択する必要があります。"

#: heksedit.rc:1128
#, c-format
msgid ""
"Could not get text from the clipboard.\n"
"Cannot continue!"
msgstr ""
"クリップボードからテキストを取得することができませんでした。\n"
"継続できません!"

#: heksedit.rc:1129
#, c-format
msgid ""
"There is text on the clipboard.\n"
"Do you want to import from\n"
"the clipboard instead of a file?"
msgstr ""
"クリップボードにテキスト データが存在します。\n"
"ファイルのかわりにクリップボードからインポートしますか?"

#: heksedit.rc:1135
#, c-format
msgid "Can't copy more bytes than are present."
msgstr "存在するより多くのデータをコピーすることはできません。"

#: heksedit.rc:1142
#, c-format
msgid "Can't cut more bytes than are present."
msgstr "存在するより多くのデータを切り取ることはできません。"

#: heksedit.rc:1143
#, c-format
msgid "Not enough memory for cutting to clipboard."
msgstr "クリップボードへ切り取りデータを格納するのに十分なメモリがありません。"

#: heksedit.rc:1144
#, c-format
msgid "Could not cut the data."
msgstr "データを切り取ることができませんでした。"

#: heksedit.rc:1150
#, c-format
msgid "Can't delete more bytes than are present."
msgstr "存在するより多くのデータを削除することはできません。"

#: heksedit.rc:1151
#, c-format
msgid "Could not delete data."
msgstr "データを削除することができませんでした。"

#: heksedit.rc:1157
#, c-format
msgid "Decimal value not recognized."
msgstr "10 進数の値が認識できません。"

#: heksedit.rc:1158
#, c-format
msgid "Number of times not recognized."
msgstr "回数が認識できません。"

#: heksedit.rc:1159
#, c-format
msgid "Invalid start offset."
msgstr "無効な開始オフセットです。"

#: heksedit.rc:1160
#, c-format
msgid "Not enough space for writing decimal values."
msgstr "10 進数データを書き込むのに十分なスペースがありません。"

#: heksedit.rc:1166
#, c-format
msgid "Number of times to paste must be at least 1."
msgstr "貼り付け回数は少なくとも1である必要があります。"

#: heksedit.rc:1167
#, c-format
msgid "Tried to paste zero-length array."
msgstr "0 バイト長の配列を貼り付けようとしました。"

#: heksedit.rc:1168
#, c-format
msgid "Not enough memory for inserting."
msgstr "挿入するのに十分なメモリがありません。"

#: heksedit.rc:1169
#, c-format
msgid "Not enough space for overwriting."
msgstr "上書きするのに十分なスペースがありません。"

#: heksedit.rc:1175
#, c-format
msgid "Can't fill a selection with a file of zero size."
msgstr "0 バイト長のファイルで選択範囲にデータを埋め込むことはできません。"

#: heksedit.rc:1176
#, c-format
msgid "Can't fill a selection with a string of zero size."
msgstr "0 バイト長の文字列で選択範囲にデータを埋め込むことはできません。"

#: heksedit.rc:1177
#, c-format
msgid "Too many bytes to fill with or some other error."
msgstr "データが多すぎまたはその他のエラーです"

#: heksedit.rc:1183
#, c-format
msgid "You have chosen an offset but it is negative, which is invalid."
msgstr "負のオフセットを指定しましたがここでは無効です。"

#: heksedit.rc:1184
#, c-format
msgid "The target value is invalid."
msgstr "移動/コピー先が不正なオフセットです。"

#: heksedit.rc:1185
#, c-format
msgid "Cannot move/copy a block of zero length."
msgstr "0 バイト長のブロックを移動/コピーすることはできません。"

#: heksedit.rc:1186
#, c-format
msgid "The chosen block extends into non-existent data."
msgstr ""
"指定されたブロックの範囲が存在しないデータの領域にまで拡張されています。"

#: heksedit.rc:1187
#, c-format
msgid "The block was not moved!"
msgstr "ブロックは移動されませんでした!"

#: heksedit.rc:1188
#, c-format
msgid "Cannot move/copy the block outside the data."
msgstr "ブロックをデータの外側に移動/コピーすることはできません。"

#: heksedit.rc:1194
#, c-format
msgid "&Size of file: %lld bytes. Load how many bytes:"
msgstr "ファイルのサイズ: %lld バイトです。 何バイト読み込みますか?(&S):"

#: heksedit.rc:1195
#, c-format
msgid "Cannot open more than 2 GB of data."
msgstr "2 GB 以上のデータは開けません。"

#: heksedit.rc:1196
#, c-format
msgid "Specified number of bytes to load greater than file size."
msgstr "ファイル サイズよりも大きな読み込みバイト数が指定されました。"

#: heksedit.rc:1197
#, c-format
msgid "Too many bytes to load."
msgstr "ロードするバイト数が多すぎます。"

#: heksedit.rc:1203
#, c-format
msgid "Cannot reverse single byte."
msgstr "1 バイト データを逆順にすることはできません。"

#: heksedit.rc:1204
#, c-format
msgid "The chosen block extends into non-existent data. The offsets will be shifted to correct positions."
msgstr ""
"指定されたブロックの範囲が存在しないデータの領域にまで拡張されています。オフ"
"セットは正しい位置にシフトされます。"

#: heksedit.rc:1210
#, c-format
msgid "Link names are \"Frhed.lnk\""
msgstr "リンク名は \"Frhed.lnk\""

#: heksedit.rc:1211
#, c-format
msgid "This folder already contains a file called \"Frhed.lnk\""
msgstr "このフォルダには既にファイル \"Frhed.lnk\" が存在しています。"

#: heksedit.rc:1212
#, c-format
msgid "\"Frhed.lnk\" can be added to this folder."
msgstr "このフォルダには \"Frhed.lnk\"  を追加することができます。"

#: heksedit.rc:1213
#, c-format
msgid "\"Frhed.lnk\" cannot be added to this folder."
msgstr "\"Frhed.lnk\" はこのフォルダに追加できません。"

#: heksedit.rc:1214
#, c-format
msgid "Frhed can start searching here."
msgstr "Frhed はここから検索できます。"

#: heksedit.rc:1215
#, c-format
msgid "Frhed cannot start the search from here."
msgstr "Frhed はここから検索を開始できません。"

#: heksedit.rc:1216
#, c-format
msgid "Could not save shortcut entries."
msgstr "ショートカット リストをレジストリに保存することができませんでした。"

#: heksedit.rc:1217
#, c-format
msgid "Cannot move more than 1 link at a time."
msgstr "一度に 1 つより多くのリンクを移動することはできません。"

#: heksedit.rc:1218
#, c-format
msgid "No link selected to move."
msgstr "移動するリンクが選択されていません。"

#: heksedit.rc:1219
#, c-format
msgid "Couldn't find the selected item."
msgstr "選択項目を見つけることができませんでした。"

#: heksedit.rc:1220
#, c-format
msgid "Place a link to Frhed in..."
msgstr "Frhed へのリンクの配置..."

#: heksedit.rc:1221
#, c-format
msgid "Move the link to Frhed to..."
msgstr "Frhed へのリンクの移動..."

#: heksedit.rc:1222
#, c-format
msgid "There is already a link in that folder."
msgstr "既にそのフォルダにはリンクが存在します。"

#: heksedit.rc:1223
#, c-format
msgid "Existing links to old versions of Frhed will be updated to this version.\nAre you sure you want to continue?"
msgstr ""
"Frhed の旧バージョンへのリンクが存在していれば、このバージョンに更新されま"
"す。続けてよろしいですか?"

#: heksedit.rc:1224
#, c-format
msgid "Pick a folder to start searching in."
msgstr "検索を開始するフォルダを指定してください。"

#: heksedit.rc:1225
#, c-format
msgid "No links selected to delete."
msgstr "削除するリンクが選択されていません。"

#: heksedit.rc:1231
#, c-format
msgid "Selection too large."
msgstr "選択範囲が広すぎます。"

#: heksedit.rc:1232
#, c-format
msgid "Could not find data."
msgstr "データを見つけることができませんでした。"

#: heksedit.rc:1233
#, c-format
msgid "Findstring is zero bytes long."
msgstr "検索文字列が 0 バイト長です。"

#: heksedit.rc:1234
#, c-format
msgid "Could not find any more occurrences."
msgstr "これ以上の出現箇所を見つけることはできませんでした。"

#: heksedit.rc:1235
#, c-format
msgid "String to find not specified."
msgstr "検索文字列が指定されていません。"

#: heksedit.rc:1241
#, c-format
msgid "You need to have Administrator privileges for getting list of drives."
msgstr "ドライブのリストを取得するには管理者権限が必要です。"

#: heksedit.rc:1242
#, c-format
msgid "Unable to open drive."
msgstr "ドライブのオープンをすることができません。"

#: heksedit.rc:1243
#, c-format
msgid "Cylinders"
msgstr "シリンダ数"

#: heksedit.rc:1244
#, c-format
msgid "Sectors"
msgstr "セクタ数"

#: heksedit.rc:1245
#, c-format
msgid "Tracks per cylinder"
msgstr "1 シリンダあたりのトラック数"

#: heksedit.rc:1246
#, c-format
msgid "Sectors per track"
msgstr "1 トラックあたりのセクタ数"

#: heksedit.rc:1247
#, c-format
msgid "Bytes per sector"
msgstr "1 セクタあたりのバイト数"

#: heksedit.rc:1248
#, c-format
msgid "Total size in bytes"
msgstr "合計バイト数"

#: heksedit.rc:1249
#, c-format
msgid "%s:Sector %I64d"
msgstr "%s:セクタ %I64d"

#: heksedit.rc:1250
#, c-format
msgid "Drive %d, Partition %d (%s)"
msgstr "ドライブ %d, パーティション %d (%s)"

#: heksedit.rc:1251
#, c-format
msgid "Drive %d (%s)"
msgstr "ドライブ %d (%s)"

#: heksedit.rc:1257
#, c-format
msgid ""
"Not enough memory to import data.\n"
"Cannot continue!\n"
"Do you want to keep what has been found so far?"
msgstr ""
"データをインポートするのに十分なメモリがありません。\n"
"継続できません!\n"
"ここまで読み取ったデータをそのままにしておきますか?"

#: heksedit.rc:1258
#, c-format
msgid ""
"The first offset found was 0x%x, which is greater than zero.\n"
"Do you want to insert %d null bytes at the start of the data?"
msgstr ""
"最初のオフセットが 0 ではなく、0x%x です。\n"
"%d 個の NUL バイトをデータの先頭に挿入しますか?"

#: heksedit.rc:1259
#, c-format
msgid ""
"Invalid offset found.\n"
"Ignore further invalid offsets?"
msgstr ""
"不正なオフセットが見つかりました。\n"
"以後不正なオフセットを無視しますか?"

#: heksedit.rc:1260
#, c-format
msgid ""
"Character data does not agree with hex data.\n"
"Ignore further mismatched data?\n"
"NB: Hex data will be used when ignoring."
msgstr ""
"文字データが 16 進データと一致していません。\n"
"以後、データの不一致を無視しますか?\n"
"注意: 無視するときは、16 進データが使用されます。"

#: heksedit.rc:1261
#, c-format
msgid ""
"Illegal character in hex data.\n"
"Cannot continue!\n"
"Do you want to keep what has been found so far?"
msgstr ""
"16進データ内に不正な文字が含まれています。\n"
"継続できません!\n"
"これまで読み取ったデータをそのままにしておきますか?"

#: heksedit.rc:1262
#, c-format
msgid "Unexpected end of data found.\nCannot continue!\nDo you want to keep what has been found so far?"
msgstr ""
"予期していないデータの終端が検出されました。\n"
"継続できません!\n"
"これまで読み取ったデータをそのままにしておきますか?"

#: heksedit.rc:1263
#, c-format
msgid ""
"Does this data have the same format as the Frhed display?\n"
"This data contains "
msgstr ""
"このデータは Frhed の画面表示と同じフォーマットですか?\n"
"このデータには次のものが含まれています: "

#: heksedit.rc:1264
#, c-format
msgid "characters other than whitespace and hexdigits (like Frhed display)."
msgstr "空白や 16 進数以外の文字 (Frhed の表示形式)。"

#: heksedit.rc:1265
#, c-format
msgid "only whitespace and hexdigits (unlike Frhed display)."
msgstr "空白と 16 進数のみ (Frhed の表示形式ではない)。"

#: heksedit.rc:1271
#, c-format
msgid "Data to replace must be selected."
msgstr "置換するデータが選択されていなければなりません。"

#: heksedit.rc:1272
#, c-format
msgid "Could not delete selected data."
msgstr "選択したデータを削除することができませんでした。"

#: heksedit.rc:1273
#, c-format
msgid "Replacing failed."
msgstr "置換に失敗しました。"

#: heksedit.rc:1274
#, c-format
msgid "Could not translate text to binary."
msgstr "テキストからバイナリに変換することができませんでした。"

#: heksedit.rc:1275
#, c-format
msgid "To-replace and replace-with data are same."
msgstr "置換対象データと置換後のデータが一致しています。"

#: heksedit.rc:1276
#, c-format
msgid "%d occurrences replaced."
msgstr "%d 個の出現箇所を置換しました。"

#: heksedit.rc:1277
#, c-format
msgid "Could not use selection as replace target."
msgstr "置換対象として選択範囲の内容を使用することができませんでした。"

#: heksedit.rc:1283
#, c-format
msgid "Selected: Offset %d=0x%x to %d=0x%x (%d byte(s))"
msgstr "選択範囲: オフセット %d=0x%x ～ %d=0x%x (%d バイト)"

#: heksedit.rc:1284
#, c-format
msgid "Offset %d=0x%x"
msgstr "オフセット %d=0x%x"

#: heksedit.rc:1285
#, c-format
msgid "Bits"
msgstr "2 進数"

#: heksedit.rc:1286
#, c-format
msgid "Unsigned"
msgstr "Unsigned"

#: heksedit.rc:1288
#, c-format
msgid "END"
msgstr "END"

#: heksedit.rc:1289
#, c-format
msgid "W"
msgstr "W"

#: heksedit.rc:1291
#, c-format
msgid "Signed"
msgstr "Signed"

#: heksedit.rc:1292
#, c-format
msgid "Size"
msgstr "サイズ"

#: heksedit.rc:1299
#, c-format
msgid "Template file:"
msgstr "テンプレート ファイル:"

#: heksedit.rc:1300
#, c-format
msgid "Applied at offset:"
msgstr "適用されるオフセット:"

#: heksedit.rc:1301
#, c-format
msgid "= %d (signed) = %u (unsigned) = 0x%x = '%c'"
msgstr "= %d (signed) = %u (unsigned) = 0x%x = '%c'"

#: heksedit.rc:1302
#: heksedit.rc:1305
#: heksedit.rc:1307
#, c-format
msgid "= %d (signed) = %u (unsigned) = 0x%x"
msgstr "= %d (signed) = %u (unsigned) = 0x%x"

#: heksedit.rc:1303
#, c-format
msgid "ERROR: not enough space for byte-size data."
msgstr "エラー: BYTE サイズのデータに十分なスペースがありません。"

#: heksedit.rc:1304
#, c-format
msgid "ERROR: missing variable name."
msgstr "エラー: 変数名が指定されていません。"

#: heksedit.rc:1306
#, c-format
msgid "ERROR: not enough space for WORD-size data."
msgstr "エラー: WORD サイズのデータに十分なスペースがありません。"

#: heksedit.rc:1308
#, c-format
msgid "ERROR: not enough space for DWORD-size data."
msgstr "エラー: DWORD サイズのデータに十分なスペースがありません。"

#: heksedit.rc:1309
#, c-format
msgid "= %f = 0x%x"
msgstr "= %f = 0x%x"

#: heksedit.rc:1310
#, c-format
msgid "ERROR: not enough space for float-size data."
msgstr "エラー: float 型サイズのデータに十分なスペースがありません。"

#: heksedit.rc:1311
#, c-format
msgid "= %g"
msgstr "= %g"

#: heksedit.rc:1312
#, c-format
msgid "ERROR: not enough space for double-size data."
msgstr "エラー: double 型サイズのデータに十分なスペースがありません。"

#: heksedit.rc:1313
#, c-format
msgid "ERROR: Unknown variable type:"
msgstr "エラー: 未知の変数の型が指定されています:"

#: heksedit.rc:1319
#, c-format
msgid "-> Length of template = %d bytes."
msgstr "-> テンプレートの長さ = %d バイト"

#: heksedit.rc:1320
#, c-format
msgid "Template files (*.tpl)\0*.tpl\0\0"
msgstr ""

#: heksedit.rc:1321
#, c-format
msgid "Choose template file"
msgstr "テンプレート ファイルの選択"

#: heksedit.rc:1322
#, c-format
msgid "Could not open template file %s."
msgstr "テンプレート ファイル %s を開くことができませんでした。"

#: heksedit.rc:1323
#, c-format
msgid "Could not load template from file %s."
msgstr "ファイル %s から テンプレートをロードすることができませんでした。"

#: heksedit.rc:1329
#, c-format
msgid "Select an item to move."
msgstr "移動する項目を選択してください。"

#: heksedit.rc:1333
#, c-format
msgid "No data to insert"
msgstr "挿入するデータがありません"

#: heksedit.rc:1334
#, c-format
msgid "TYMED_ISTORAGE is not supported for drag-drop."
msgstr "TYMED_ISTORAGE のドラッグ&&ドロップはサポートされていません。"

#: heksedit.rc:1340
#, c-format
msgid "Float size value:\n%g\n"
msgstr ""
"単精度浮動小数点数:\n"
"%g\n"

#: heksedit.rc:1341
#, c-format
msgid "Not enough space for float size value.\n"
msgstr "単精度浮動小数点数に十分なスペースがありません。\n"

#: heksedit.rc:1342
#, c-format
msgid "\nDouble size value:\n%g\n"
msgstr ""
"\n"
"倍精度浮動小数点数:\n"
"%g\n"

#: heksedit.rc:1343
#, c-format
msgid ""
"\n"
"Not enough space for double size value.\n"
msgstr ""
"\n"
"倍精度浮動小数点数に十分なスペースがありません。\n"

#: heksedit.rc:1344
#, c-format
msgid "File name and path: "
msgstr "ファイル名とパス:"

#: heksedit.rc:1345
#, c-format
msgid ""
"\n"
"Partially opened at offset 0x%x = %d.\n"
"Number of bytes read: %d = %d kilobytes.\n"
msgstr ""
"\n"
"部分的にオフセット 0x%x = %d を開きました。\n"
"読み取りバイト数: %d = %d KB \n"

#: heksedit.rc:1346
#, c-format
msgid ""
"\n"
"File size: %d bytes = %d kilobytes.\n"
msgstr ""
"\n"
"ファイル サイズ: %d バイト = %d KB\n"

#: heksedit.rc:1347
#, c-format
msgid ""
"\n"
"Number of hexdump lines: %d.\n"
msgstr ""
"\n"
"16進ダンプの行数: %d.\n"

#: heksedit.rc:1348
#, c-format
msgid "This file could not be accessed and will be removed from the MRU list."
msgstr ""
"このファイルにアクセスすることができませんでした。このファイルは MRU リストか"
"ら削除されます。"

#: heksedit.rc:1349
#, c-format
msgid "Really reset colors to default values?"
msgstr "本当に色をデフォルト値にリセットしますか?"

#: heksedit.rc:1350
#, c-format
msgid "Are you sure you want to delete this file?"
msgstr "本当にこのファイルを削除しますか?"

#: heksedit.rc:1351
#, c-format
msgid "Could not delete file."
msgstr "ファイルを削除することができませんでした。"

#: heksedit.rc:1352
#, c-format
msgid "Really adopt the operating system colour scheme?"
msgstr "本当に OS のカラー スキーマを適用しますか?"

#: heksedit.rc:1358
#, c-format
msgid "Do you want to save your changes?"
msgstr "変更を保存しますか?"

#: heksedit.rc:1364
#, c-format
msgid "Hexdump saved."
msgstr "16 進ダンプが保存されました。"

#: heksedit.rc:1365
#, c-format
msgid "Could not save Hexdump."
msgstr "16 進ダンプを保存することができませんでした。"

#: heksedit.rc:1366
#, c-format
msgid "Not enough memory for copying hexdump to clipboard."
msgstr ""
"16 進ダンプをクリップボードにコピーするのに必要なメモリが不足しています。"

#: heksedit.rc:1367
#, c-format
msgid "Could not copy hexdump to clipboard."
msgstr "クリップボードへ16進ダンプをコピーすることができませんでした。"

#: heksedit.rc:1368
#, c-format
msgid "Hex Dump files(*.txt,*.hex)|*.txt;*.hex|All Files (*.*)|*.*|"
msgstr ""

