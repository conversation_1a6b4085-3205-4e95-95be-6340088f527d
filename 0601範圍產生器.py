import tkinter as tk
from tkinter import messagebox
import win32clipboard

# 取得 RTF 的 Clipboard Format ID
CF_RTF = win32clipboard.RegisterClipboardFormat("Rich Text Format")

def encode_rtf_unicode(text):
    # 將字串轉成 RTF Unicode escape 格式，例如 "～" 變成 \u65374?
    rtf_encoded = ""
    for ch in text:
        code = ord(ch)
        if code <= 127:
            # ASCII 字元直接加入
            rtf_encoded += ch
        else:
            # 非 ASCII 用 \uN? 格式 (signed 16-bit)
            if code > 32767:
                code -= 65536  # 轉成 signed 16-bit
            rtf_encoded += f"\\u{code}?"
    return rtf_encoded

def copy_rtf_bold(text):
    encoded_text = encode_rtf_unicode(text)
    rtf_text = r"{\rtf1\ansi\deff0{\fonttbl{\f0 Arial;}}\fs28\b " + encoded_text + r"\b0}"
    win32clipboard.OpenClipboard()
    win32clipboard.EmptyClipboard()
    win32clipboard.SetClipboardData(CF_RTF, rtf_text.encode('utf-8'))
    win32clipboard.CloseClipboard()

class CopyRangeApp:
    def __init__(self, master):
        self.master = master
        self.master.title("範圍複製器（Word 粗體全形支援）")
        self.master.attributes("-topmost", True)  # 視窗永遠最上層

        tk.Label(master, text="起始：").grid(row=0, column=0, padx=5, pady=5)
        self.start_entry = tk.Entry(master, width=5)
        self.start_entry.grid(row=0, column=1)
        self.start_entry.insert(0, "45")

        tk.Label(master, text="結束：").grid(row=0, column=2, padx=5, pady=5)
        self.end_entry = tk.Entry(master, width=5)
        self.end_entry.grid(row=0, column=3)
        self.end_entry.insert(0, "56")

        self.set_button = tk.Button(master, text="設定範圍", command=self.set_range)
        self.set_button.grid(row=0, column=4, padx=5)

        self.copy_button = tk.Button(
            master, text="",
            command=self.copy_next,
            font=("Arial", 18), width=15, height=2,
            state=tk.DISABLED
        )
        self.copy_button.grid(row=1, column=0, columnspan=5, pady=20)

        self.current = None
        self.start = None
        self.end = None

    def set_range(self):
        try:
            self.start = int(self.start_entry.get())
            self.end = int(self.end_entry.get())
            if self.start > self.end:
                raise ValueError("起始數字不能大於結束數字")
            self.current = self.start
            self.update_button_text()
            self.copy_button.config(state=tk.NORMAL)
        except ValueError as e:
            messagebox.showerror("錯誤", f"請輸入正確的數字範圍\n{e}")

    def update_button_text(self):
        if self.current <= self.end:
            next_text = f"[～{self.current}]"
            self.copy_button.config(text=next_text)
        else:
            self.copy_button.config(text="完成", state=tk.DISABLED)

    def copy_next(self):
        if self.current is None or self.current > self.end:
            messagebox.showinfo("完成", "所有數字都已複製完畢！")
            return

        text = f"[～{self.current}]"
        copy_rtf_bold(text)
        self.current += 1
        self.update_button_text()

if __name__ == "__main__":
    import win32clipboard
    CF_RTF = win32clipboard.RegisterClipboardFormat("Rich Text Format")

    root = tk.Tk()
    app = CopyRangeApp(root)
    root.mainloop()
