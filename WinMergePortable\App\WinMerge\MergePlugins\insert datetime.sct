<scriptlet>

<implements type="Automation" id="dispatcher">
	<property name="PluginEvent">
		<get/>
	</property>
	<property name="PluginDescription">
		<get/>
	</property>
	<property name="PluginExtendedProperties">
		<get/>
	</property>
	<method name="InsertDate"/>
	<method name="InsertTime"/>
</implements>

<script language="VBS">
Option Explicit

Function get_PluginEvent()
	get_PluginEvent = "EDITOR_SCRIPT"
End Function

Function get_PluginDescription()
	get_PluginDescription = "Basic text functions for the context menu"
End Function

Function get_PluginFileFilters()
	get_PluginFileFilters= ".*"
End Function

Function get_PluginExtendedProperties()
	get_PluginExtendedProperties = "InsertDate.MenuCaption=Insert Date;InsertTime.MenuCaption=Insert Time"
End Function

' transformation functions
Function InsertDate(Text)
	InsertDate = Text + FormatDateTime(Date, 1)
End Function

Function InsertTime(Text)
	InsertTime = Text + FormatDateTime(Time, 4)
End Function

</script>
</scriptlet>
