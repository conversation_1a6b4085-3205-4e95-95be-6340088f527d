// ==UserScript==
// @name         Yahoo News Auto Clicker
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  自動滾動網頁並點擊領取按鈕，然後連續點擊新聞連結
// <AUTHOR>
// @match        https://tw.news.yahoo.com/*
// @match        https://tw.sports.yahoo.com/news/*
// @match        https://tw.stock.yahoo.com/news/*
// @match        https://login.yahoo.com/*
// @grant        none
// ==/UserScript==

(function() {
    'use strict';

    // 儲存已點擊過的連結，避免重複
    let clickedLinks = JSON.parse(localStorage.getItem('clickedYahooLinks') || '[]');
    let clickCount = parseInt(localStorage.getItem('yahooClickCount') || '0');
    const maxClicks = 15;

    // 儲存當前嘗試點擊的連結
    let currentAttemptLink = localStorage.getItem('currentAttemptLink') || null;

    // 添加隨機延遲函數，模擬人類行為（5分鐘內完成）
    function randomDelay(min = 800, max = 1500) {
        const delay = Math.floor(Math.random() * (max - min + 1)) + min;
        return new Promise(resolve => setTimeout(resolve, delay));
    }

    // 檢測是否在登入頁面
    function isLoginPage() {
        return window.location.href.includes('login.yahoo.com');
    }

    // 處理登入頁面
    function handleLoginPage() {
        console.log('🚨 檢測到登入頁面...');

        // 如果有當前嘗試的連結，直接跳轉到該連結
        if (currentAttemptLink) {
            console.log('重新嘗試點擊新聞連結:', currentAttemptLink);
            window.location.href = currentAttemptLink;
            return true;
        }

        // 否則返回上一頁或跳轉到新聞首頁
        if (window.history.length > 1) {
            console.log('返回上一頁...');
            window.history.back();
        } else {
            console.log('跳轉到新聞首頁...');
            window.location.href = 'https://tw.news.yahoo.com/';
        }

        return true;
    }

    // 等待頁面載入完成
    function waitForElement(selector, timeout = 10000) {
        return new Promise((resolve, reject) => {
            const startTime = Date.now();
            const checkElement = () => {
                const element = document.querySelector(selector);
                if (element) {
                    resolve(element);
                } else if (Date.now() - startTime > timeout) {
                    reject(new Error(`Element ${selector} not found within ${timeout}ms`));
                } else {
                    setTimeout(checkElement, 100);
                }
            };
            checkElement();
        });
    }

    // 自動滾動頁面（快速但自然的滾動方式）
    function autoScroll() {
        return new Promise(async (resolve) => {
            let scrollHeight = document.body.scrollHeight;
            let currentScroll = 0;
            const scrollStep = Math.floor(Math.random() * 200) + 250; // 適中的滾動步長

            console.log('開始滾動頁面...');

            while (currentScroll < scrollHeight && currentScroll < 3000) {
                window.scrollBy(0, scrollStep);
                currentScroll += scrollStep;

                // 較短的隨機延遲
                await new Promise(resolve => setTimeout(resolve, Math.floor(Math.random() * 300) + 200));

                // 檢查是否有新內容載入
                if (document.body.scrollHeight > scrollHeight) {
                    scrollHeight = document.body.scrollHeight;
                }
            }

            // 等待內容載入
            console.log('滾動完成，等待內容載入...');
            await randomDelay(1000, 2000);
            resolve();
        });
    }

    // 尋找並點擊領取按鈕
    async function clickReceiveButton() {
        try {
            console.log('尋找領取按鈕...');
            
            // 多種可能的領取按鈕選擇器
            const buttonSelectors = [
                'button[data-ylk*="article-challenge"][data-ylk*="login"]:contains("領取")',
                'button.bg-brand-yahoo-purple:contains("領取")',
                'button[class*="purple"]:contains("領取")',
                'button:contains("領取")'
            ];

            let button = null;
            for (let selector of buttonSelectors) {
                // 由於 :contains 不是標準CSS選擇器，我們需要手動查找
                const buttons = document.querySelectorAll('button');
                for (let btn of buttons) {
                    if (btn.textContent.includes('領取') && 
                        (btn.getAttribute('data-ylk')?.includes('article-challenge') || 
                         btn.className.includes('purple'))) {
                        button = btn;
                        break;
                    }
                }
                if (button) break;
            }

            if (button) {
                console.log('找到領取按鈕，準備點擊...');
                button.scrollIntoView({ behavior: 'smooth', block: 'center' });
                await new Promise(resolve => setTimeout(resolve, 1000));
                button.click();
                console.log('已點擊領取按鈕');
                return true;
            } else {
                console.log('未找到領取按鈕');
                return false;
            }
        } catch (error) {
            console.error('點擊領取按鈕時發生錯誤:', error);
            return false;
        }
    }

    // 尋找符合條件且未重複的新聞連結（.html前面有9個數字）- 只找第一個
    function findFirstNewsLink() {
        const links = document.querySelectorAll('a[href*=".html"]');
        console.log(`檢查 ${links.length} 個連結...`);
        console.log(`已點擊過的連結數量: ${clickedLinks.length}`);

        for (let link of links) {
            const href = link.getAttribute('href');
            // 檢查是否符合格式：.html前面有9個數字
            const match = href.match(/(\d{9})\.html$/);

            if (match) {
                console.log(`檢查連結: ${href}`);

                // 確保連結沒有被點擊過
                if (!clickedLinks.includes(href)) {
                    const validLink = {
                        element: link,
                        href: href,
                        text: link.textContent.trim()
                    };
                    console.log(`✅ 找到未重複的符合條件新聞連結: ${validLink.text}`);
                    console.log(`連結網址: ${validLink.href}`);
                    return validLink;
                } else {
                    console.log(`❌ 連結已點擊過，跳過: ${href}`);
                }
            }
        }

        console.log('❌ 沒有找到未重複且符合條件的新聞連結');
        return null;
    }

    // 直接導航到新聞連結
    async function clickNewsLink(link) {
        try {
            console.log(`準備導航到新聞連結: ${link.text}`);

            // 記錄已點擊的連結
            clickedLinks.push(link.href);
            clickCount++;

            // 儲存到 localStorage
            localStorage.setItem('clickedYahooLinks', JSON.stringify(clickedLinks));
            localStorage.setItem('yahooClickCount', clickCount.toString());

            // 儲存當前嘗試的連結，以防跳轉到登入頁面
            currentAttemptLink = link.href;
            localStorage.setItem('currentAttemptLink', currentAttemptLink);

            console.log(`✅ 導航到第 ${clickCount} 個新聞連結: ${link.href}`);

            // 直接導航到新聞連結
            window.location.href = link.href;

            return true;
        } catch (error) {
            console.error('導航到新聞連結時發生錯誤:', error);
            return false;
        }
    }

    // 主要執行函數（自然的行為模式）
    async function main() {
        try {
            console.log('Yahoo Auto Clicker 開始執行...');
            console.log(`目前已點擊 ${clickCount}/${maxClicks} 個連結`);

            // 檢查是否在登入頁面
            if (isLoginPage()) {
                handleLoginPage();
                return;
            }

            if (clickCount >= maxClicks) {
                console.log('已達到最大點擊次數，停止執行');
                return;
            }

            // 等待頁面載入
            console.log('等待頁面載入...');
            await randomDelay(1500, 2500);

            // 自動滾動頁面
            console.log('開始滾動頁面...');
            await autoScroll();

            // 嘗試點擊領取按鈕
            console.log('尋找領取按鈕...');
            const buttonClicked = await clickReceiveButton();

            if (buttonClicked) {
                // 等待頁面反應
                console.log('已點擊領取按鈕，等待頁面反應...');
                await randomDelay(1000, 2000);
                console.log('現在尋找下一個新聞連結...');
            } else {
                console.log('未找到領取按鈕，直接尋找新聞連結...');
            }

            // 尋找第一個符合條件且未重複的新聞連結
            console.log('開始尋找未重複的新聞連結...');
            const newsLink = findFirstNewsLink();

            if (newsLink && clickCount < maxClicks) {
                console.log(`準備導航到連結 (${clickCount + 1}/${maxClicks}): ${newsLink.text}`);

                // 直接導航到新聞連結
                const success = await clickNewsLink(newsLink);

                if (success) {
                    // 導航成功後，腳本會在新頁面重新開始執行
                    console.log('✅ 連結導航成功，等待頁面載入...');
                } else {
                    // 如果導航失敗，快速重試
                    console.log('❌ 導航失敗，等待 3 秒後重新嘗試...');
                    setTimeout(main, 3000);
                }
            } else if (clickCount >= maxClicks) {
                console.log(`🎉 已完成所有 ${maxClicks} 次點擊，腳本執行完畢！`);
                // 清除當前嘗試的連結
                localStorage.removeItem('currentAttemptLink');
                currentAttemptLink = null;
            } else {
                console.log('❌ 沒有找到更多未重複且符合條件的連結');
                // 如果沒有找到連結，快速重新嘗試
                if (clickCount < maxClicks) {
                    console.log('等待 5 秒後重新嘗試...');
                    setTimeout(main, 5000);
                }
            }

        } catch (error) {
            console.error('執行過程中發生錯誤:', error);
            // 發生錯誤時快速重試
            console.log('發生錯誤，等待 5 秒後重新嘗試...');
            setTimeout(main, 5000);
        }
    }

    // 添加重置功能（可在控制台執行）
    window.resetYahooClicker = function() {
        localStorage.removeItem('clickedYahooLinks');
        localStorage.removeItem('yahooClickCount');
        localStorage.removeItem('currentAttemptLink');
        clickedLinks = [];
        clickCount = 0;
        currentAttemptLink = null;
        console.log('Yahoo Auto Clicker 已重置');
    };

    // 頁面載入完成後開始執行
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            // 如果是登入頁面，立即處理
            if (isLoginPage()) {
                setTimeout(handleLoginPage, 2000);
            } else {
                setTimeout(main, 1000);
            }
        });
    } else {
        // 如果是登入頁面，立即處理
        if (isLoginPage()) {
            setTimeout(handleLoginPage, 2000);
        } else {
            setTimeout(main, 1000);
        }
    }

    console.log('Yahoo Auto Clicker 腳本已載入');
    console.log('如需重置點擊記錄，請在控制台執行: resetYahooClicker()');

    // 監聽頁面變化，如果跳轉到登入頁面則處理
    const originalPushState = history.pushState;
    const originalReplaceState = history.replaceState;

    history.pushState = function() {
        originalPushState.apply(history, arguments);
        setTimeout(() => {
            if (isLoginPage()) {
                handleLoginPage();
            }
        }, 1000);
    };

    history.replaceState = function() {
        originalReplaceState.apply(history, arguments);
        setTimeout(() => {
            if (isLoginPage()) {
                handleLoginPage();
            }
        }, 1000);
    };

    // 監聽 URL 變化
    window.addEventListener('popstate', () => {
        setTimeout(() => {
            if (isLoginPage()) {
                handleLoginPage();
            }
        }, 1000);
    });

})();
